# ================================
# Docker环境专用配置文件
# 适配容器化部署
# ================================

# 项目相关配置
vim:
  roll:
    external:
      # 从环境变量读取外部接口地址
      dev-url: ${VIM_ROLL_EXTERNAL_DEV_URL:http://192.168.64.132:5190/api}
      prod-url: ${VIM_ROLL_EXTERNAL_PROD_URL:https://www.voltskins.top/api}
  security:
    server-secret-key: ${SERVER_SECRET_KEY:EXWSP4X8P58ZXYB4KPA5J4T3UMZRTDQFMP4JQ2XUGE7Q2M23YJ33RJR8UZW26FA2}
  seed-config:
    valid-chars: ${VALIA_CHARS:0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ}
    server-seed-length: ${SERVER_SEED_LENGTH:128}
    user-seed-length: ${USER_SEED_LENGTH:64}
    salt-length: ${SALT_LENGTH:30}

# 系统角色配置
system:
  role:
    anchor: 105

ruoyi:
  name: RuoYi
  version: 3.8.9
  copyrightYear: 2025
  # Docker环境文件路径
  profile: ${VIM_UPLOAD_PATH:/app/upload}
  addressEnabled: false
  captchaType: math

# 服务器配置
server:
  port: ${SERVER_PORT:8080}
  servlet:
    context-path: /
  tomcat:
    uri-encoding: UTF-8
    accept-count: 1000
    threads:
      max: 800
      min-spare: 100

# 用户配置
user:
  password:
    maxRetryCount: 5
    lockTime: 10

# Spring配置
spring:
  # 激活Docker环境配置
  profiles:
    active: docker
  messages:
    basename: i18n/messages
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 20MB
  devtools:
    restart:
      enabled: ${DEVTOOLS_ENABLED:false}
  
  # Redis配置（容器化）
  redis:
    host: ${REDIS_HOST:redis}
    port: ${REDIS_PORT:6379}
    database: 0
    password: ${REDIS_PASSWORD:vimboxredis123@}
    timeout: 60s
    command-timeout: 30s
    lettuce:
      pool:
        min-idle: ${REDIS_MIN_IDLE:5}
        max-idle: ${REDIS_MAX_IDLE:32}
        max-active: ${REDIS_MAX_ACTIVE:64}
        max-wait: 10s
      shutdown-timeout: 200ms
  
  # 数据源配置（容器化）
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    druid:
      # 主库数据源
      master:
        url: jdbc:mysql://${MYSQL_HOST:mysql-master}:${MYSQL_PORT:3306}/${MYSQL_DATABASE:vimbox}?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=false&serverTimezone=GMT%2B8
        username: ${MYSQL_USERNAME:vimbox}
        password: ${MYSQL_PASSWORD:Vimbox123@}
      # 从库数据源
      slave:
        enabled: true
        url: jdbc:mysql://${MYSQL_SLAVE_HOST:mysql-slave}:3306/${MYSQL_SLAVE_DATABASE:autopw}?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=false&serverTimezone=GMT%2B8
        username: ${MYSQL_USERNAME:vimbox}
        password: ${MYSQL_PASSWORD:Vimbox123@}
      
      # 连接池配置
      initialSize: ${DB_INITIAL_SIZE:10}
      minIdle: ${DB_MIN_IDLE:10}
      maxActive: ${DB_MAX_ACTIVE:100}
      maxWait: 10000
      connectTimeout: 15000
      socketTimeout: 30000
      timeBetweenEvictionRunsMillis: 60000
      minEvictableIdleTimeMillis: 300000
      maxEvictableIdleTimeMillis: 600000
      validationQuery: SELECT 1
      testWhileIdle: true
      testOnBorrow: true
      testOnReturn: false
      validationQueryTimeout: 3
      asyncInit: true
      keepAlive: true
      phyTimeoutMillis: 60000
      phyMaxUseCount: 2000
      
      # 初始化SQL
      initConnectionSqls:
        - "SET SESSION sql_mode='STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO'"
        - "SET SESSION innodb_lock_wait_timeout=20"
        - "SET SESSION transaction_isolation='READ-COMMITTED'"
        - "SET SESSION wait_timeout=28800"
        - "SET SESSION interactive_timeout=28800"
      
      removeAbandoned: false
      removeAbandonedTimeout: 180
      logAbandoned: true
      
      # 监控配置
      webStatFilter:
        enabled: true
        exclusions: "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*"
      statViewServlet:
        enabled: true
        url-pattern: /druid/*
        reset-enable: false
        login-username: ${DRUID_LOGIN_USERNAME:admin}
        login-password: ${DRUID_LOGIN_PASSWORD:druid123}
        allow: 127.0.0.1,192.168.0.0/16,172.16.0.0/12,10.0.0.0/8
      filter:
        stat:
          enabled: true
          log-slow-sql: true
          slow-sql-millis: 500
          merge-sql: true
        wall:
          enabled: true
          config:
            multi-statement-allow: true

# Token配置
token:
  header: Authorization
  secret: abcdefghijklmnopqrstuvwxyz
  expireTime: 1440

# MyBatis配置
mybatis:
  typeAliasesPackage: com.ruoyi.project.**.domain
  mapperLocations: classpath*:mybatis/**/*Mapper.xml
  configLocation: classpath:mybatis/mybatis-config.xml

# PageHelper分页插件
pagehelper:
  helperDialect: mysql
  supportMethodsArguments: true
  params: count=countSql
  reasonable: true
  auto-runtime-dialect: true

# Swagger配置
swagger:
  enabled: ${SWAGGER_ENABLED:true}
  pathMapping: /dev-api
  lazy-init: true

# XSS防护
xss:
  enabled: true
  excludes: /system/notice
  urlPatterns: /system/*,/monitor/*,/tool/*

# 代码生成
gen:
  author: ruoyi
  packageName: com.ruoyi.project.system
  autoRemovePre: false
  tablePrefix: sys_
  allowOverwrite: false

# 价格预警配置
price-alert:
  performance:
    batch-query:
      batch-size: 100
      max-batch-size: 500
      batch-insert-size: 100
      enabled: true
    cache:
      price-info-ttl: 300
      item-info-ttl: 600
      enabled: true
      max-size: 10000
    connection-pool:
      monitor-interval: 30000
      leak-detection-threshold: 60000
      monitor-enabled: true
    execution:
      max-items-per-execution: 1000
      timeout-seconds: 300
      thread-pool-size: 4
      async-enabled: true
      retry-count: 3
      retry-interval: 1000

# 日志配置
logging:
  level:
    com.ruoyi: ${LOG_LEVEL:INFO}
    org.springframework: warn
    com.ruoyi.project.priceAlert: INFO
    org.springframework.dao: ERROR
    org.springframework.transaction.interceptor: ERROR

# Spring Boot Actuator健康检查
management:
  endpoints:
    web:
      exposure:
        include: health,info
  endpoint:
    health:
      show-details: when-authorized
