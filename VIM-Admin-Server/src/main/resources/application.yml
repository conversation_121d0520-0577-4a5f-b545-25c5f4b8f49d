# 项目相关配置
vim:
  roll:
    external:
      # 开发环境外部接口地址
#      dev-url: http://192.168.1.46:3123/api
      dev-url: https://www.voltskins.top/api
      # 生产环境外部接口地址
      prod-url: http://192.168.1.46:3123/api
  # ==================== 安全配置 ====================
  security:
    # 从环境变量读取
    server-secret-key: ${SERVER_SECRET_KEY:EXWSP4X8P58ZXYB4KPA5J4T3UMZRTDQFMP4JQ2XUGE7Q2M23YJ33RJR8UZW26FA2}
  # ==================== 种子配置 ====================
  seed-config:
    valid-chars: ${VALIA_CHARS:0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ}
    server-seed-length: ${SERVER_SEED_LENGTH:128}
    user-seed-length: ${USER_SEED_LENGTH:64}
    salt-length: ${SALT_LENGTH:30}
  # ==================== 任务恢复配置 ====================
  task:
    recovery:
      # 是否启用任务恢复系统
      enabled: true
      # 是否启用异步恢复（推荐）
      async-recovery: true
      # 系统启动后延迟多久开始恢复（毫秒）
      startup-delay: 3000
      # 恢复操作超时时间（毫秒）
      recovery-timeout: 120000
      # 是否启用性能监控
      performance-monitoring: true
      # 是否启用健康检查
      health-check: true
      # 健康检查间隔（毫秒）
      health-check-interval: 300000
      # 是否启用详细日志
      verbose-logging: false
      # 全局最大重试次数
      max-retries: 3
      # 重试间隔（毫秒）
      retry-interval: 5000

# 系统角色配置
system:
  role:
    anchor: 105  # 主播角色ID
ruoyi:
  # 名称
  name: RuoYi
  # 版本
  version: 3.8.9
  # 版权年份
  copyrightYear: 2025
  # 文件路径 示例（ Windows配置D:/ruoyi/uploadPath，Linux配置 /home/<USER>/uploadPath）
  #  profile: www/voltskins-web/image
  profile: /www/voltskins-web/image
  # 获取ip地址开关
  addressEnabled: false
  # 验证码类型 math 数字计算 char 字符验证
  captchaType: math
# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 8080
  servlet:
    # 应用的访问路径
    context-path: /
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000
    threads:
      # tomcat最大线程数，默认为200
      max: 800
      # Tomcat启动初始化的线程数，默认值10
      min-spare: 100
# 用户配置
user:
  password:
    # 密码最大错误次数
    maxRetryCount: 5
    # 密码锁定时间（默认10分钟）
    lockTime: 10

# Spring配置
spring:
  profiles:
    active: ${SPRING_PROFILES_ACTIVE:prod}
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 10MB
      # 设置总上传的文件大小
      max-request-size: 20MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true
  # redis 配置优化 - 开发环境
  redis:
    # 地址
    host: *************
    # 端口，默认为6379
    port: 6379
    # 数据库索引
    database: 0
    # 密码
    password: vimboxredis123@
    # 连接超时时间
    timeout: 30s
    # 命令超时时间
    command-timeout: 10s
    lettuce:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 2
        # 连接池中的最大空闲连接
        max-idle: 16
        # 连接池的最大数据库连接数（开发环境适中配置）
        max-active: 32
        # 连接池最大阻塞等待时间
        max-wait: 5s
      # 关闭超时时间
      shutdown-timeout: 100ms
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    druid:
      # 主库数据源
      master:
        url: jdbc:mysql://*************:3306/vimbox?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8
        username: vimbox
        password: Vimbox123@
      # 从库数据源（价格参考数据）
      slave:
        enabled: true
        url: ***************************************************************************************************************************************************
        username: vimbox
        password: Vimbox123@
      # ==================== 开发环境连接池优化配置 ====================
      # 连接池大小优化 - 开发环境适中配置
      initialSize: 5               # 初始连接数（开发环境较小）
      minIdle: 5                   # 最小空闲连接数（开发环境较小）
      maxActive: 30                # 最大连接数（开发环境适中）
      # 超时时间优化 - 开发环境快速失败
      maxWait: 3000                # 获取连接等待超时（3秒）
      connectTimeout: 5000         # 连接超时（5秒，开发环境较短）
      socketTimeout: 3000          # 网络超时（3秒）
      # 连接生命周期优化 - 开发环境快速回收
      timeBetweenEvictionRunsMillis: 30000      # 检测间隔（30秒）
      minEvictableIdleTimeMillis: 120000        # 最小生存时间（2分钟，开发环境较短）
      maxEvictableIdleTimeMillis: 180000        # 最大生存时间（3分钟，开发环境较短）
      # 连接验证优化 - 确保连接有效性
      validationQuery: SELECT 1
      testWhileIdle: true
      testOnBorrow: false          # 开发环境关闭借用时验证，提高性能
      testOnReturn: false
      validationQueryTimeout: 1    # 验证查询超时（1秒）
      # 连接池预热和异步初始化
      asyncInit: true
      # MySQL保活机制 - 防止连接超时
      keepAlive: true
      phyTimeoutMillis: 30000      # 物理连接超时30秒
      phyMaxUseCount: 1000         # 物理连接最大使用次数
      # 连接初始化SQL - 设置MySQL会话级别优化参数
      initConnectionSqls:
        - "SET SESSION sql_mode='STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO'"
        - "SET SESSION innodb_lock_wait_timeout=10"           # 锁等待超时10秒
        - "SET SESSION transaction_isolation='READ-COMMITTED'" # 设置读已提交隔离级别
      # 连接泄漏检测和清理 - 开发环境快速检测
      removeAbandoned: true        # 启用连接泄漏检测
      removeAbandonedTimeout: 120  # 连接泄漏超时时间（2分钟，开发环境较短）
      logAbandoned: true          # 记录连接泄漏日志

      # ==================== 监控和统计配置 ====================
      webStatFilter:
        enabled: true
        exclusions: "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*"

      statViewServlet:
        enabled: true
        url-pattern: /druid/*
        reset-enable: false
        login-username: admin
        login-password: druid123
        allow: 127.0.0.1,192.168.1.0/24
      filter:
        stat:
          enabled: true
          log-slow-sql: true
          slow-sql-millis: 500      # 死锁优化：降低慢SQL阈值（原来1000 -> 500ms）
          merge-sql: true
        wall:
          enabled: true
          config:
            multi-statement-allow: true

# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: abcdefghijklmnopqrstuvwxyz
  # 令牌有效期（默认30分钟）
  expireTime: 1440

# MyBatis配置
mybatis:
  # 搜索指定包别名
  typeAliasesPackage: com.ruoyi.project.**.domain

  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:mybatis/**/*Mapper.xml
  # 加载全局的配置文件
  configLocation: classpath:mybatis/mybatis-config.xml

# PageHelper分页插件
pagehelper:
  helperDialect: mysql
  supportMethodsArguments: true
  params: count=countSql
  reasonable: true
  auto-runtime-dialect: true

# Swagger配置
swagger:
  # 是否开启swagger（启动优化：开发环境可考虑禁用以加快启动）
  enabled: false
  # 请求前缀
  pathMapping: /dev-api
  # 启动优化：延迟初始化Swagger文档
  lazy-init: true

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*

# 代码生成
gen:
  # 作者
  author: ruoyi
  # 默认生成包路径 system 需改成自己的模块名称 如 system monitor tool
  packageName: com.ruoyi.project.system
  # 自动去除表前缀，默认是true
  autoRemovePre: false
  # 表前缀（生成类名不会包含表前缀，多个用逗号分隔）
  tablePrefix: sys_
  # 是否允许生成文件覆盖到本地（自定义路径），默认不允许
  allowOverwrite: false

# 价格预警性能优化配置
price-alert:
  performance:
    batch-query:
      batch-size: 100
      max-batch-size: 500
      batch-insert-size: 100
      enabled: true
    cache:
      price-info-ttl: 300
      item-info-ttl: 600
      enabled: true
      max-size: 10000
    connection-pool:
      monitor-interval: 30000
      leak-detection-threshold: 60000
      monitor-enabled: true
    execution:
      max-items-per-execution: 1000
      timeout-seconds: 300
      thread-pool-size: 4
      async-enabled: true
      retry-count: 3
      retry-interval: 1000
# 日志配置优化
logging:
  level:
    com.ruoyi: debug
    org.springframework: warn