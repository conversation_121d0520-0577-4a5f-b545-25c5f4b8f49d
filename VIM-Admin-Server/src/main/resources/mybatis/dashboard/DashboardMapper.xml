<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.dashboard.mapper.DashboardMapper">



    <resultMap type="DashboardLatestBoxRecordDTO" id="DashboardLatestBoxRecordResult">
        <result property="oid"    column="oid"    />
        <result property="uid"    column="uid"    />
        <result property="boxName"    column="boxname"    />
        <result property="itemId"    column="itemid"    />
        <result property="itemName"    column="itemname"    />
        <result property="itemLevel"    column="itemlevel"    />
        <result property="price"    column="price"    />
        <result property="openTime"    column="open_time"    />
    </resultMap>

    <resultMap type="DashboardHotBoxDTO" id="DashboardHotBoxResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="imageBox"    column="image_box"    />
        <result property="price"    column="price"    />
        <result property="saleVolume"    column="sale_volume"    />
        <result property="saleAmount"    column="sale_amount"    />
        <result property="referencePrice"    column="reference_price"    />
        <result property="returnRate"    column="return_rate"    />
    </resultMap>

    <!-- 获取总开箱次数 -->
    <select id="getTotalBoxOpenings" resultType="Long">
        SELECT COUNT(*) FROM vim_order_box vob
        <if test="streamerFilter != null and streamerFilter != 'all'">
            INNER JOIN vim_user vu ON vob.uid = vu.id
        </if>
        WHERE vob.type = 1
        <if test="streamerFilter != null and streamerFilter != 'all'">
            <choose>
                <when test="streamerFilter == 'exclude'">
                    AND vu.identity = 1
                </when>
                <when test="streamerFilter == 'only'">
                    AND vu.identity IN (2, 3)
                </when>
            </choose>
        </if>
        <if test="startDateTime != null">
            AND CAST(vob.timestamp AS UNSIGNED) &gt;= #{startDateTime}
        </if>
        <if test="endDateTime != null">
            AND CAST(vob.timestamp AS UNSIGNED) &lt;= #{endDateTime}
        </if>
    </select>

    <!-- 获取总销售额 -->
    <select id="getTotalSales" resultType="String">
        SELECT COALESCE(SUM(vob.price), 0) FROM vim_order_box vob
        <if test="streamerFilter != null and streamerFilter != 'all'">
            INNER JOIN vim_user vu ON vob.uid = vu.id
        </if>
        WHERE vob.type = 1
        <if test="streamerFilter != null and streamerFilter != 'all'">
            <choose>
                <when test="streamerFilter == 'exclude'">
                    AND vu.identity = 1
                </when>
                <when test="streamerFilter == 'only'">
                    AND vu.identity IN (2, 3)
                </when>
            </choose>
        </if>
        <if test="startDateTime != null">
            AND CAST(vob.timestamp AS UNSIGNED) &gt;= #{startDateTime}
        </if>
        <if test="endDateTime != null">
            AND CAST(vob.timestamp AS UNSIGNED) &lt;= #{endDateTime}
        </if>
    </select>

    <!-- 获取活跃用户数 -->
    <select id="getActiveUsers" resultType="Long">
        SELECT COUNT(DISTINCT vob.uid) FROM vim_order_box vob
        <if test="streamerFilter != null and streamerFilter != 'all'">
            INNER JOIN vim_user vu ON vob.uid = vu.id
        </if>
        WHERE vob.type = 1
        <if test="streamerFilter != null and streamerFilter != 'all'">
            <choose>
                <when test="streamerFilter == 'exclude'">
                    AND vu.identity = 1
                </when>
                <when test="streamerFilter == 'only'">
                    AND vu.identity IN (2, 3)
                </when>
            </choose>
        </if>
        AND vob.timestamp &gt;= UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL #{days} DAY)) * 1000
        <if test="startDateTime != null">
            AND CAST(vob.timestamp AS UNSIGNED) &gt;= #{startDateTime}
        </if>
        <if test="endDateTime != null">
            AND CAST(vob.timestamp AS UNSIGNED) &lt;= #{endDateTime}
        </if>
    </select>

    <!-- 获取开箱数据趋势 -->
    <select id="getBoxTrend" resultType="java.util.Map" parameterType="java.util.Map">
        <if test="timeType == 'day'">
            SELECT
                DATE_FORMAT(FROM_UNIXTIME(timestamp / 1000), '%Y-%m-%d %H:00') AS time_label,
                COUNT(*) AS opening_count,
                SUM(price) AS sales_amount
            FROM vim_order_box
            WHERE type = 1
            <!-- 优化时间戳查询，使用索引友好的条件 -->
            <choose>
                <when test="startDate != null and startDate != '' and endDate != null and endDate != ''">
                    AND timestamp &gt;= UNIX_TIMESTAMP(CONCAT(#{startDate}, ' 00:00:00')) * 1000
                    AND timestamp &lt;= UNIX_TIMESTAMP(CONCAT(#{endDate}, ' 23:59:59')) * 1000
                </when>
                <otherwise>
                    AND timestamp &gt;= UNIX_TIMESTAMP(CONCAT(CURDATE(), ' 00:00:00')) * 1000
                    AND timestamp &lt;= UNIX_TIMESTAMP(CONCAT(CURDATE(), ' 23:59:59')) * 1000
                </otherwise>
            </choose>
            GROUP BY time_label
            ORDER BY time_label ASC
            LIMIT #{limit}
        </if>
        <if test="timeType == 'week'">
            SELECT
                DATE_FORMAT(FROM_UNIXTIME(timestamp / 1000), '%Y-%m-%d') AS time_label,
                COUNT(*) AS opening_count,
                SUM(price) AS sales_amount
            FROM vim_order_box
            WHERE type = 1
            <!-- 优化时间戳查询，使用索引友好的条件 -->
            <choose>
                <when test="startDate != null and startDate != '' and endDate != null and endDate != ''">
                    AND timestamp &gt;= UNIX_TIMESTAMP(CONCAT(#{startDate}, ' 00:00:00')) * 1000
                    AND timestamp &lt;= UNIX_TIMESTAMP(CONCAT(#{endDate}, ' 23:59:59')) * 1000
                </when>
                <otherwise>
                    AND timestamp &gt;= UNIX_TIMESTAMP(DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) DAY)) * 1000
                    AND timestamp &lt;= UNIX_TIMESTAMP(DATE_ADD(DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) DAY), INTERVAL 6 DAY) + INTERVAL 1 DAY - INTERVAL 1 SECOND) * 1000
                </otherwise>
            </choose>
            GROUP BY time_label
            ORDER BY time_label ASC
            LIMIT #{limit}
        </if>
        <if test="timeType == 'month'">
            SELECT
                DATE_FORMAT(FROM_UNIXTIME(timestamp / 1000), '%Y-%m-%d') AS time_label,
                COUNT(*) AS opening_count,
                SUM(price) AS sales_amount
            FROM vim_order_box
            WHERE type = 1
            <!-- 优化时间戳查询，使用索引友好的条件 -->
            <choose>
                <when test="startDate != null and startDate != '' and endDate != null and endDate != ''">
                    AND timestamp &gt;= UNIX_TIMESTAMP(CONCAT(#{startDate}, ' 00:00:00')) * 1000
                    AND timestamp &lt;= UNIX_TIMESTAMP(CONCAT(#{endDate}, ' 23:59:59')) * 1000
                </when>
                <otherwise>
                    AND timestamp &gt;= UNIX_TIMESTAMP(DATE_FORMAT(CURDATE(), '%Y-%m-01 00:00:00')) * 1000
                    AND timestamp &lt;= UNIX_TIMESTAMP(LAST_DAY(CURDATE()) + INTERVAL 1 DAY - INTERVAL 1 SECOND) * 1000
                </otherwise>
            </choose>
            GROUP BY time_label
            ORDER BY time_label ASC
            LIMIT #{limit}
        </if>
    </select>

    <!-- 获取开箱结果分布 -->
    <select id="getBoxDistribution" resultType="java.util.Map" parameterType="java.util.Map">
        WITH total_count AS (
            SELECT COUNT(*) AS total_openings
            FROM vim_order_box
            WHERE type = 1
            <if test="startDate != null and startDate != ''">
                AND timestamp &gt;= UNIX_TIMESTAMP(CONCAT(#{startDate}, ' 00:00:00')) * 1000
            </if>
            <if test="endDate != null and endDate != ''">
                AND timestamp &lt;= UNIX_TIMESTAMP(CONCAT(#{endDate}, ' 23:59:59')) * 1000
            </if>
        )
        SELECT
            itemlevel AS item_level,
            COUNT(*) AS count,
            ROUND(COUNT(*) * 100.0 / NULLIF(tc.total_openings, 0), 2) AS percentage
        FROM vim_order_box vob
        CROSS JOIN total_count tc
        WHERE vob.type = 1
        <if test="startDate != null and startDate != ''">
            AND vob.timestamp &gt;= UNIX_TIMESTAMP(CONCAT(#{startDate}, ' 00:00:00')) * 1000
        </if>
        <if test="endDate != null and endDate != ''">
            AND vob.timestamp &lt;= UNIX_TIMESTAMP(CONCAT(#{endDate}, ' 23:59:59')) * 1000
        </if>
        GROUP BY itemlevel, tc.total_openings
        ORDER BY itemlevel
    </select>

    <!-- 获取最新开箱记录 -->
    <select id="getLatestBoxRecords" resultMap="DashboardLatestBoxRecordResult" parameterType="java.util.Map">
        SELECT
            oid,
            uid,
            boxname,
            itemid,
            itemname,
            itemlevel,
            price,
            FROM_UNIXTIME(timestamp / 1000, '%Y-%m-%d %H:%i:%s') AS open_time
        FROM vim_order_box
        WHERE type = 1 AND `show` = 1
        <if test="startDate != null and startDate != ''">
            AND timestamp &gt;= UNIX_TIMESTAMP(CONCAT(#{startDate}, ' 00:00:00')) * 1000
        </if>
        <if test="endDate != null and endDate != ''">
            AND timestamp &lt;= UNIX_TIMESTAMP(CONCAT(#{endDate}, ' 23:59:59')) * 1000
        </if>
        ORDER BY timestamp DESC
        LIMIT #{limit}
    </select>

    <!-- 获取热门盲盒排行 -->
    <select id="getHotBoxes" resultMap="DashboardHotBoxResult" parameterType="java.util.Map">
        SELECT
            b.id,
            b.name,
            b.image_box AS image_box,
            b.price,
            b.sale_volume AS sale_volume,
            b.sale_amount AS sale_amount,
            0.00 AS reference_price,
            0.00 AS return_rate
        FROM vim_box b
        WHERE (b.delete_time IS NULL OR b.delete_time = 0) AND b.sale = 1
        ORDER BY b.sale_volume DESC
        LIMIT #{limit}
    </select>

    <!-- 获取盲盒奖品价格统计数据 -->
    <select id="getBoxPrizeStats" resultType="java.util.Map" parameterType="java.util.Map">
        SELECT
            COUNT(*) AS total_box_openings,
            COALESCE(SUM(i.price_show), 0) AS total_prize_value,
            COALESCE(AVG(i.price_show), 0) AS average_prize_value,
            COALESCE(MAX(i.price_show), 0) AS max_prize_value,
            COALESCE(MIN(i.price_show), 0) AS min_prize_value
        FROM vim_order_box b
        INNER JOIN vim_item i ON b.itemid = i.id
            AND (i.delete_time IS NULL OR i.delete_time = 0)
        WHERE b.type = 1
        <if test="startDateTime != null">
            AND b.timestamp &gt;= #{startDateTime}
        </if>
        <if test="endDateTime != null">
            AND b.timestamp &lt;= #{endDateTime}
        </if>
    </select>

    <!-- 获取按盲盒分组的开箱统计数据 -->
    <select id="getBoxOpeningStatsByBox" resultType="java.util.Map" parameterType="java.util.Map">
        SELECT
            boxid AS box_id,
            COUNT(*) AS opening_count
        FROM vim_order_box
        WHERE type = 1 AND boxid > 0
        <if test="startDateTime != null">
            AND timestamp &gt;= #{startDateTime}
        </if>
        <if test="endDateTime != null">
            AND timestamp &lt;= #{endDateTime}
        </if>
        GROUP BY boxid
        HAVING COUNT(*) > 0
        ORDER BY opening_count DESC
    </select>
    <!-- 获取总提货金额 -->
    <select id="getTotalClaimAmount" resultType="String">
        SELECT COALESCE(SUM(vi.price_show), 0)
        FROM vim_order_claim voc
        INNER JOIN vim_item vi ON voc.itemid = vi.id
            AND (vi.delete_time IS NULL OR vi.delete_time = 0)
        <if test="streamerFilter != null and streamerFilter != 'all'">
            INNER JOIN vim_user vu ON voc.uid = vu.id
        </if>
        WHERE voc.state = 1
        <if test="streamerFilter != null and streamerFilter != 'all'">
            <choose>
                <when test="streamerFilter == 'exclude'">
                    AND vu.identity = 1
                </when>
                <when test="streamerFilter == 'only'">
                    AND vu.identity IN (2, 3)
                </when>
            </choose>
        </if>
        <if test="startDateTime != null">
            AND voc.creat_time * 1000 &gt;= #{startDateTime}
        </if>
        <if test="endDateTime != null">
            AND voc.creat_time * 1000 &lt;= #{endDateTime}
        </if>
    </select>

    <!-- 获取总充值电能 -->
    <select id="getTotalRechargeAmount" resultType="String">
        SELECT COALESCE(SUM(vor.amount), 0) FROM vim_order_recharge vor
        <if test="streamerFilter != null and streamerFilter != 'all'">
            INNER JOIN vim_user vu ON vor.uid = vu.id
        </if>
        WHERE vor.state = 2
        <if test="streamerFilter != null and streamerFilter != 'all'">
            <choose>
                <when test="streamerFilter == 'exclude'">
                    AND vu.identity = 1
                </when>
                <when test="streamerFilter == 'only'">
                    AND vu.identity IN (2, 3)
                </when>
            </choose>
        </if>
        <if test="startDateTime != null">
            AND vor.create_time * 1000 &gt;= #{startDateTime}
        </if>
        <if test="endDateTime != null">
            AND vor.create_time * 1000 &lt;= #{endDateTime}
        </if>
    </select>

    <!-- 获取利润金额 -->
    <select id="getProfitAmount" resultType="String">
        SELECT COALESCE(
            payment_revenue.total_revenue - claim_costs.total_costs,
            0
        ) AS profit_amount
        FROM (
            SELECT COALESCE(SUM(vop.amount), 0) AS total_revenue
            FROM vim_order_recharge vop
            <if test="streamerFilter != null and streamerFilter != 'all'">
                INNER JOIN vim_user vu ON vop.uid = vu.id
            </if>
            WHERE vop.state = 2
            <if test="streamerFilter != null and streamerFilter != 'all'">
                <choose>
                    <when test="streamerFilter == 'exclude'">
                        AND vu.identity = 1
                    </when>
                    <when test="streamerFilter == 'only'">
                        AND vu.identity IN (2, 3)
                    </when>
                </choose>
            </if>
            <if test="startDateTime != null">
                AND CAST(vop.create_time AS UNSIGNED) * 1000 &gt;= #{startDateTime}
            </if>
            <if test="endDateTime != null">
                AND CAST(vop.create_time AS UNSIGNED) * 1000 &lt;= #{endDateTime}
            </if>
        ) payment_revenue
        CROSS JOIN (
            SELECT COALESCE(SUM(voc.cost), 0) AS total_costs
            FROM vim_order_claim voc
            <if test="streamerFilter != null and streamerFilter != 'all'">
                INNER JOIN vim_user vu2 ON voc.uid = vu2.id
            </if>
            WHERE 1 = 1
            <if test="streamerFilter != null and streamerFilter != 'all'">
                <choose>
                    <when test="streamerFilter == 'exclude'">
                        AND vu2.identity = 1
                    </when>
                    <when test="streamerFilter == 'only'">
                        AND vu2.identity IN (2, 3)
                    </when>
                </choose>
            </if>
            <if test="startDateTime != null">
                AND CAST(voc.claim_time AS UNSIGNED) * 1000 &gt;= #{startDateTime}
            </if>
            <if test="endDateTime != null">
                AND CAST(voc.claim_time AS UNSIGNED) * 1000 &lt;= #{endDateTime}
            </if>
        ) claim_costs
    </select>

    <!-- 获取加权平均返奖率 -->
    <select id="getWeightedAverageReturnRate" resultType="java.util.Map">
        SELECT
            COALESCE(
                SUM(box_stats.opening_count * box_stats.return_rate) / NULLIF(SUM(box_stats.opening_count), 0),
                0
            ) AS weighted_average_return_rate,
            SUM(box_stats.opening_count) AS total_openings,
            COUNT(DISTINCT box_stats.boxid) AS box_count
        FROM (
            SELECT
                vob.boxid,
                COUNT(*) AS opening_count,
                COALESCE(box_return_rates.return_rate, 0) AS return_rate
            FROM vim_order_box vob
            INNER JOIN (
                SELECT
                    vb.id AS boxid,
                    vb.price AS box_price,
                    (SUM(vbi.probability * vi.price_show / 100000.0) / vb.price) * 100 AS return_rate
                FROM vim_box vb
                INNER JOIN vim_box_item vbi ON vb.id = vbi.id_box
                INNER JOIN vim_item vi ON vbi.id_item = vi.id
                    AND (vi.delete_time IS NULL OR vi.delete_time = 0)
                    AND vi.price_show > 0
                WHERE (vb.delete_time IS NULL OR vb.delete_time = 0)
                    AND vb.price > 0
                GROUP BY vb.id, vb.price
                HAVING SUM(vbi.probability) > 0
            ) box_return_rates ON vob.boxid = box_return_rates.boxid
            WHERE vob.type = 1 AND vob.boxid > 0
            <if test="startDateTime != null">
                AND CAST(vob.timestamp AS UNSIGNED) &gt;= #{startDateTime}
            </if>
            <if test="endDateTime != null">
                AND CAST(vob.timestamp AS UNSIGNED) &lt;= #{endDateTime}
            </if>
            GROUP BY vob.boxid, box_return_rates.return_rate
            HAVING COUNT(*) > 0
        ) AS box_stats
    </select>



    <!-- 获取订单发货表实际发货金额 -->
    <select id="getTotalShippingCost" resultType="String" parameterType="java.util.Map">
        SELECT COALESCE(SUM(voc.cost), 0)
        FROM vim_order_claim voc
        <if test="streamerFilter != null and streamerFilter != 'all'">
            INNER JOIN vim_user vu ON voc.uid = vu.id
        </if>
        WHERE voc.cost IS NOT NULL
        <if test="streamerFilter != null and streamerFilter != 'all'">
            <choose>
                <when test="streamerFilter == 'exclude'">
                    AND vu.identity = 1
                </when>
                <when test="streamerFilter == 'only'">
                    AND vu.identity IN (2, 3)
                </when>
            </choose>
        </if>
        <if test="startDateTime != null">
            AND CAST(voc.claim_time AS UNSIGNED) * 1000 &gt;= #{startDateTime}
        </if>
        <if test="endDateTime != null">
            AND CAST(voc.claim_time AS UNSIGNED) * 1000 &lt;= #{endDateTime}
        </if>
    </select>

    <!-- 调用存储过程获取统计报告 -->
    <select id="getStatisticsReport" resultType="java.util.Map" parameterType="java.util.Map">
        {CALL GetStatisticsReport(#{startTime}, #{endTime}, #{isStreamer})}
    </select>

    <!-- 获取总充值笔数 -->
    <select id="getTotalRechargeCount" resultType="Long">
        SELECT COUNT(*) FROM vim_order_recharge vor
        <if test="streamerFilter != null and streamerFilter != 'all'">
            INNER JOIN vim_user vu ON vor.uid = vu.id
        </if>
        WHERE vor.state = 2
        <if test="streamerFilter != null and streamerFilter != 'all'">
            <choose>
                <when test="streamerFilter == 'exclude'">
                    AND vu.identity = 1
                </when>
                <when test="streamerFilter == 'only'">
                    AND vu.identity IN (2, 3)
                </when>
            </choose>
        </if>
        <if test="startDateTime != null">
            AND vor.create_time * 1000 &gt;= #{startDateTime}
        </if>
        <if test="endDateTime != null">
            AND vor.create_time * 1000 &lt;= #{endDateTime}
        </if>
    </select>

    <!-- 获取新增用户数量（主播推广用户） -->
    <select id="getNewUsersCount" resultType="Long">
        SELECT COUNT(DISTINCT vu.id) FROM vim_user vu
        <!-- 🔑 关键：通过推广关系过滤，只统计当前主播的下级用户 -->
        LEFT JOIN vim_user anchor ON vu.invite_user = anchor.id
        LEFT JOIN sys_user su ON anchor.phone = su.phonenumber AND su.del_flag = '0'
        LEFT JOIN sys_dept d ON su.dept_id = d.dept_id
        WHERE 1=1
        <!-- 🔑 核心过滤条件：只统计有推广关系的用户（invite_user不为空） -->
        AND vu.invite_user IS NOT NULL
        <!-- 🎯 如果指定了anchorId，直接按主播ID过滤 -->
        <if test="params.anchorId != null">
            AND vu.invite_user = #{params.anchorId}
        </if>
        <if test="streamerFilter != null and streamerFilter != 'all'">
            <choose>
                <when test="streamerFilter == 'exclude'">
                    AND vu.identity = 1
                </when>
                <when test="streamerFilter == 'only'">
                    AND vu.identity IN (2, 3)
                </when>
            </choose>
        </if>
        <if test="startDateTime != null">
            AND vu.create_time * 1000 &gt;= #{startDateTime}
        </if>
        <if test="endDateTime != null">
            AND vu.create_time * 1000 &lt;= #{endDateTime}
        </if>
        <!-- 🔑 数据权限控制：通过主播的sys_user关联实现权限过滤 -->
        <!-- 注意：如果指定了anchorId，数据权限控制仍然生效，确保用户只能查看有权限的主播数据 -->
        ${params.dataScope}
    </select>

    <!-- 获取新增用户详情列表（主播推广用户） -->
    <select id="getNewUsersDetail" resultType="java.util.Map">
        SELECT
            vu.id as userId,
            vu.nickname,
            vu.phone,
            vu.create_time as registerTime,
            vu.identity,
            vu.invite_user as inviteUserId,
            anchor.nickname as inviteUserName,
            first_recharge.create_time as firstRechargeTime,
            first_recharge.amount as firstRechargeAmount
        FROM vim_user vu
        <!-- 🔑 关键：通过推广关系关联主播信息 -->
        LEFT JOIN vim_user anchor ON vu.invite_user = anchor.id
        LEFT JOIN sys_user su ON anchor.phone = su.phonenumber AND su.del_flag = '0'
        LEFT JOIN sys_dept d ON su.dept_id = d.dept_id
        <!-- 🔑 LEFT JOIN首次充值记录：获取每个用户的第一条充值记录 -->
        LEFT JOIN (
            SELECT
                vor.uid,
                vor.create_time,
                vor.amount,
                ROW_NUMBER() OVER (PARTITION BY vor.uid ORDER BY vor.create_time ASC) as rn
            FROM vim_order_recharge vor
            WHERE vor.state = 2
        ) first_recharge ON vu.id = first_recharge.uid AND first_recharge.rn = 1
        WHERE 1=1
        <!-- 🔑 核心过滤条件：只查询有推广关系的用户（invite_user不为空） -->
        AND vu.invite_user IS NOT NULL
        <!-- 🎯 如果指定了anchorId，直接按主播ID过滤 -->
        <if test="params.anchorId != null">
            AND vu.invite_user = #{params.anchorId}
        </if>
        <if test="streamerFilter != null and streamerFilter != 'all'">
            <choose>
                <when test="streamerFilter == 'exclude'">
                    AND vu.identity = 1
                </when>
                <when test="streamerFilter == 'only'">
                    AND vu.identity IN (2, 3)
                </when>
            </choose>
        </if>
        <if test="startDateTime != null">
            AND vu.create_time * 1000 &gt;= #{startDateTime}
        </if>
        <if test="endDateTime != null">
            AND vu.create_time * 1000 &lt;= #{endDateTime}
        </if>
        <!-- 🔑 数据权限控制：通过主播的sys_user关联实现权限过滤 -->
        <!-- 注意：如果指定了anchorId，数据权限控制仍然生效，确保用户只能查看有权限的主播数据 -->
        ${params.dataScope}
        ORDER BY vu.create_time DESC
    </select>
</mapper>