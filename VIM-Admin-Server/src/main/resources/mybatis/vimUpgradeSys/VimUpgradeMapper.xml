<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.vimUpgradeSys.mapper.VimUpgradeMapper">
    
    <resultMap type="com.ruoyi.project.vimUpgradeSys.domain.VimUpgrade" id="VimUpgradeResult">
        <result property="id"    column="id"    />
        <result property="item"    column="item"    />
        <result property="type"    column="type"    />
    </resultMap>
    
    <resultMap type="com.ruoyi.project.vimUpgradeSys.domain.dto.VimUpgradeItemDto" id="VimUpgradeItemDtoResult">
        <!-- 映射 vimItem 对象 -->
        <association property="vimItem" javaType="com.ruoyi.project.commoditySys.domain.VimItem">
            <result property="id" column="vi_id" />
            <result property="name" column="vi_name" />
            <result property="hashname" column="vi_hashname" />
            <result property="priceShow" column="vi_priceShow" />
            <result property="image" column="vi_image" />
        </association>

        <association property="vimUpgrade" javaType="com.ruoyi.project.vimUpgradeSys.domain.VimUpgrade">
            <result property="id" column="vu_id" />
            <result property="item" column="vu_item" />
            <result property="type" column="vu_type" />
        </association>
    </resultMap>

    <sql id="selectVimUpgradeVo">
        select id, item, type from vim_upgrade
    </sql>
    
    <sql id="selectVimUpgradeItemVo">
        SELECT
            vu.id as vu_id,
            vu.item as vu_item,
            vu.type as vu_type,
            vi.id as vi_id,
            vi.name as vi_name,
            vi.hashname as vi_hashname,
            vi.price_show as vi_priceShow,
            vi.image as vi_image
        FROM
            vim_upgrade vu
                JOIN
            vim_item vi ON vu.item = vi.id
    </sql>
    
    <!-- 用于分页计数的SQL -->
    <sql id="countVimUpgradeItemVo">
        SELECT
            COUNT(1)
        FROM
            vim_upgrade vu
                JOIN
            vim_item vi ON vu.item = vi.id
    </sql>

    <select id="selectVimUpgradeById" parameterType="Long" resultMap="VimUpgradeResult">
        <include refid="selectVimUpgradeVo"/>
        where id = #{id}
    </select>
    
    <select id="selectVimUpgradeItemById" parameterType="Long" resultMap="VimUpgradeItemDtoResult">
        <include refid="selectVimUpgradeItemVo"/>
        where vu.id = #{id}
    </select>
    
    <select id="selectVimUpgradeList" parameterType="com.ruoyi.project.vimUpgradeSys.domain.VimUpgrade" resultMap="VimUpgradeResult">
        <include refid="selectVimUpgradeVo"/>
        <where>  
            <if test="item != null "> and item = #{item}</if>
            <if test="type != null and type != ''"> and type = #{type}</if>
        </where>
    </select>
    
    <select id="selectVimUpgradeItemList" parameterType="com.ruoyi.project.vimUpgradeSys.domain.VimUpgrade" resultMap="VimUpgradeItemDtoResult">
        <include refid="selectVimUpgradeItemVo"/>
        <where>  
            <if test="item != null "> and vu.item = #{item}</if>
            <if test="name != null and name != ''"> and vi.name like concat('%', #{name}, '%')</if>
            <if test="type != null and type != ''"> and vu.type = #{type}</if>
        </where>
    </select>
    
    <!-- 添加计数方法 -->
    <select id="countVimUpgradeItemList" parameterType="com.ruoyi.project.vimUpgradeSys.domain.VimUpgrade" resultType="java.lang.Integer">
        <include refid="countVimUpgradeItemVo"/>
        <where>  
            <if test="item != null "> and vu.item = #{item}</if>
            <if test="name != null and name != ''"> and vi.name like concat('%', #{name}, '%')</if>
            <if test="type != null and type != ''"> and vu.type = #{type}</if>
        </where>
    </select>
    
    <insert id="insertVimUpgrade" parameterType="com.ruoyi.project.vimUpgradeSys.domain.VimUpgrade" useGeneratedKeys="true" keyProperty="id">
        insert into vim_upgrade
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="item != null">item,</if>
            <if test="type != null and type != ''">type,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="item != null">#{item},</if>
            <if test="type != null and type != ''">#{type},</if>
         </trim>
    </insert>

    <update id="updateVimUpgrade" parameterType="com.ruoyi.project.vimUpgradeSys.domain.VimUpgrade">
        update vim_upgrade
        <trim prefix="SET" suffixOverrides=",">
            <if test="item != null">item = #{item},</if>
            <if test="type != null and type != ''">type = #{type},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteVimUpgradeById" parameterType="Long">
        delete from vim_upgrade where id = #{id}
    </delete>

    <delete id="deleteVimUpgradeByIds" parameterType="String">
        delete from vim_upgrade where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper> 