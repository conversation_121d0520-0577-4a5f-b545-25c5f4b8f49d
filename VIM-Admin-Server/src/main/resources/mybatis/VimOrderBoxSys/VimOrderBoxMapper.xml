<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.VimOrderBoxSys.mapper.VimOrderBoxMapper">
    
    <resultMap type="VimOrderBox" id="VimOrderBoxResult">
        <result property="oid"    column="oid"    />
        <result property="payid"    column="payid"    />
        <result property="uid"    column="uid"    />
        <result property="boxname"    column="boxname"    />
        <result property="itemid"    column="itemid"    />
        <result property="itemname"    column="itemname"    />
        <result property="itemlevel"    column="itemlevel"    />
        <result property="price"    column="price"    />
        <result property="timestamp"    column="timestamp"    />
        <result property="userseed"    column="userseed"    />
        <result property="serverseed"    column="serverseed"    />
        <result property="vimOrderBoxrange"    column="range"    />
        <result property="random"    column="random"    />
        <result property="state"    column="state"    />
        <result property="type"    column="type"    />
        <result property="show"    column="show"    />
    </resultMap>

    <!-- 订单扩展结果映射，包含用户信息 -->
    <resultMap type="com.ruoyi.project.VimOrderBoxSys.domain.vo.VimOrderBoxVO" id="VimOrderBoxVOResult" extends="VimOrderBoxResult">
        <result property="userNickname"    column="user_nickname"    />
        <result property="userPhoneRaw"    column="user_phone"    />
    </resultMap>

    <sql id="selectVimOrderBoxVo">
        select oid, payid, uid, boxname, itemid, itemname, itemlevel, price, timestamp, userseed, serverseed, `range`, random, state from vim_order_box
    </sql>

    <!-- 包含用户信息的订单查询SQL -->
    <sql id="selectVimOrderBoxWithUserVo">
        select
            vob.oid, vob.payid, vob.uid, vob.boxname, vob.itemid, vob.itemname,
            vob.itemlevel, vob.price, vob.timestamp, vob.userseed, vob.serverseed,
            vob.`range`, vob.random, vob.state, vob.type, vob.`show`,
            vu.nickname as user_nickname,
            vu.phone as user_phone
        from vim_order_box vob
        left join vim_user vu on vob.uid = vu.id
    </sql>

    <select id="selectVimOrderBoxList" parameterType="VimOrderBox" resultMap="VimOrderBoxResult">
        <include refid="selectVimOrderBoxVo"/>
        <where>
            <if test="oid != null  and oid != ''"> and oid = #{oid}</if>
            <if test="payid != null  and payid != ''"> and payid = #{payid}</if>
            <if test="uid != null "> and uid = #{uid}</if>
            <if test="boxname != null  and boxname != ''"> and boxname like concat('%', #{boxname}, '%')</if>
            <if test="state != null "> and state = #{state}</if>
             and (boxname != '兑换商城' OR boxname IS NULL)
        </where>
        ORDER BY CAST(timestamp AS UNSIGNED) DESC
    </select>

    <!-- 包含用户信息的订单列表查询 -->
    <select id="selectVimOrderBoxWithUserList" parameterType="VimOrderBox" resultMap="VimOrderBoxVOResult">
        <include refid="selectVimOrderBoxWithUserVo"/>
        <where>
            <if test="oid != null  and oid != ''"> and vob.oid = #{oid}</if>
            <if test="payid != null  and payid != ''"> and vob.payid = #{payid}</if>
            <if test="uid != null "> and vob.uid = #{uid}</if>
            <if test="boxname != null  and boxname != ''"> and vob.boxname like concat('%', #{boxname}, '%')</if>
            <if test="state != null "> and vob.state = #{state}</if>
            <if test="type != null "> and vob.type = #{type}</if>
            and (vob.boxname != '兑换商城' OR vob.boxname IS NULL)
        </where>
        ORDER BY CAST(vob.timestamp AS UNSIGNED) DESC
    </select>
    <select id="selectVimExchangeList" parameterType="VimOrderBox" resultMap="VimOrderBoxResult">
        <include refid="selectVimOrderBoxVo"/>
        <where>
            <if test="oid != null  and oid != ''"> and oid = #{oid}</if>
            <if test="payid != null  and payid != ''"> and payid = #{payid}</if>
            <if test="uid != null "> and uid = #{uid}</if>
            <if test="boxname != null  and boxname != ''"> and boxname like concat('%', #{boxname}, '%')</if>
            <if test="itemname != null  and itemname != ''"> and itemname like concat('%', #{itemname}, '%')</if>
            <if test="state != null "> and state = #{state}</if>
            and boxname = '兑换商城'
        </where>
        ORDER BY CAST(timestamp AS UNSIGNED) DESC
    </select>

    <!-- 包含用户信息的兑换列表查询 -->
    <select id="selectVimExchangeWithUserList" parameterType="VimOrderBox" resultMap="VimOrderBoxVOResult">
        <include refid="selectVimOrderBoxWithUserVo"/>
        <where>
            <if test="oid != null  and oid != ''"> and vob.oid = #{oid}</if>
            <if test="payid != null  and payid != ''"> and vob.payid = #{payid}</if>
            <if test="uid != null "> and vob.uid = #{uid}</if>
            <if test="boxname != null  and boxname != ''"> and vob.boxname like concat('%', #{boxname}, '%')</if>
            <if test="itemname != null  and itemname != ''"> and vob.itemname like concat('%', #{itemname}, '%')</if>
            <if test="state != null "> and vob.state = #{state}</if>
            and vob.boxname = '兑换商城'
        </where>
        ORDER BY CAST(vob.timestamp AS UNSIGNED) DESC
    </select>
    
    <select id="selectVimOrderBoxByOid" parameterType="String" resultMap="VimOrderBoxResult">
        <include refid="selectVimOrderBoxVo"/>
        where oid = #{oid}
    </select>

    <insert id="insertVimOrderBox" parameterType="VimOrderBox">
        insert into vim_order_box
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="oid != null">oid,</if>
            <if test="payid != null">payid,</if>
            <if test="uid != null">uid,</if>
            <if test="boxname != null">boxname,</if>
            <if test="itemid != null">itemid,</if>
            <if test="itemname != null">itemname,</if>
            <if test="itemlevel != null">itemlevel,</if>
            <if test="price != null">price,</if>
            <if test="timestamp != null">timestamp,</if>
            <if test="userseed != null">userseed,</if>
            <if test="serverseed != null">serverseed,</if>
            <if test="vimOrderBoxrange != null">`range`,</if>
            <if test="random != null">random,</if>
            <if test="type != null">type,</if>
            <if test="show != null">`show`,</if>
            <if test="state != null">state,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="oid != null">#{oid},</if>
            <if test="payid != null">#{payid},</if>
            <if test="uid != null">#{uid},</if>
            <if test="boxname != null">#{boxname},</if>
            <if test="itemid != null">#{itemid},</if>
            <if test="itemname != null">#{itemname},</if>
            <if test="itemlevel != null">#{itemlevel},</if>
            <if test="price != null">#{price},</if>
            <if test="timestamp != null">#{timestamp},</if>
            <if test="userseed != null">#{userseed},</if>
            <if test="serverseed != null">#{serverseed},</if>
            <if test="vimOrderBoxrange != null">#{vimOrderBoxrange},</if>
            <if test="random != null">#{random},</if>
            <if test="type != null">#{type},</if>
            <if test="show != null">#{show},</if>
            <if test="state != null">#{state},</if>
         </trim>
    </insert>

    <update id="updateVimOrderBox" parameterType="VimOrderBox">
        update vim_order_box
        <trim prefix="SET" suffixOverrides=",">
            <if test="payid != null">payid = #{payid},</if>
            <if test="uid != null">uid = #{uid},</if>
            <if test="boxname != null">boxname = #{boxname},</if>
            <if test="itemid != null">itemid = #{itemid},</if>
            <if test="itemname != null">itemname = #{itemname},</if>
            <if test="itemlevel != null">itemlevel = #{itemlevel},</if>
            <if test="price != null">price = #{price},</if>
            <if test="timestamp != null">timestamp = #{timestamp},</if>
            <if test="userseed != null">userseed = #{userseed},</if>
            <if test="serverseed != null">serverseed = #{serverseed},</if>
            <if test="vimOrderBoxrange != null">`range` = #{vimOrderBoxrange},</if>
            <if test="random != null">random = #{random},</if>
            <if test="type != null">type = #{type},</if>
            <if test="show != null">`show` = #{show},</if>
            <if test="state != null">`state` = #{state},</if>

        </trim>
        where oid = #{oid}
    </update>

    <delete id="deleteVimOrderBoxByOid" parameterType="String">
        delete from vim_order_box where oid = #{oid}
    </delete>

    <delete id="deleteVimOrderBoxByOids" parameterType="String">
        delete from vim_order_box where oid in
        <foreach item="oid" collection="array" open="(" separator="," close=")">
            #{oid}
        </foreach>
    </delete>
</mapper>