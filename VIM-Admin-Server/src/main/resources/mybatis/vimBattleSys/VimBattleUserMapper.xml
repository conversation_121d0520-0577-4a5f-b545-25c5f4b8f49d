<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.vimBattleSys.mapper.VimBattleUserMapper">
    
    <resultMap type="VimBattleUser" id="VimBattleUserResult">
        <result property="id"    column="id"    />
        <result property="idUser"    column="id_user"    />
        <result property="idBattle"    column="id_battle"    />
        <result property="jointime"    column="jointime"    />
    </resultMap>

    <resultMap type="VimBattleUserDTO" id="VimBattleUserDTOResult" extends="VimBattleUserResult">
        <result property="userNickname"    column="user_nickname"    />
        <result property="userLevel"    column="user_level"    />
        <result property="userIdentity"    column="user_identity"    />
        <result property="userIdentityDesc"    column="user_identity_desc"    />
        <result property="userCoin"    column="user_coin"    />
        <result property="battleState"    column="battle_state"    />
        <result property="battleStateDesc"    column="battle_state_desc"    />
        <result property="battlePrice"    column="battle_price"    />
        <result property="battleMode"    column="battle_mode"    />
        <result property="battleModeDesc"    column="battle_mode_desc"    />
        <result property="participationDuration"    column="participation_duration"    />
        <result property="battleUserMax"    column="battle_user_max"    />
        <result property="battleUserNow"    column="battle_user_now"    />
    </resultMap>

    <sql id="selectVimBattleUserVo">
        select id, id_user, id_battle, jointime from vim_battle_user
    </sql>

    <sql id="selectVimBattleUserDTOVo">
        select 
            vbu.id, vbu.id_user, vbu.id_battle, vbu.jointime,
            vu.nickname as user_nickname,
            vu.level as user_level,
            vu.identity as user_identity,
            case vu.identity 
                when 1 then '普通用户'
                when 2 then '线上主播'
                when 3 then '线下主播'
                else '未知'
            end as user_identity_desc,
            vu.coin as user_coin,
            vb.state as battle_state,
            case vb.state 
                when 1 then '等待中'
                when 2 then '进行中'
                when 3 then '已结束'
                else '未知'
            end as battle_state_desc,
            vb.price as battle_price,
            vb.mode as battle_mode,
            case vb.mode 
                when 1 then '欧皇'
                when 2 then '非酋'
                when 3 then '天选'
                when 4 then '夺魁'
                else '未知'
            end as battle_mode_desc,
            case when vb.start_time is not null and vb.start_time > 0 
                then round((vb.start_time - vbu.jointime) / 60, 0)
                else round((unix_timestamp() - vbu.jointime) / 60, 0)
            end as participation_duration,
            vb.user_max as battle_user_max,
            vb.user_now as battle_user_now
        from vim_battle_user vbu
        left join vim_user vu on vbu.id_user = vu.id
        left join vim_battle vb on vbu.id_battle = vb.id
    </sql>

    <select id="selectVimBattleUserList" parameterType="VimBattleUser" resultMap="VimBattleUserResult">
        <include refid="selectVimBattleUserVo"/>
        <where>  
            <if test="idUser != null "> and id_user = #{idUser}</if>
            <if test="idBattle != null "> and id_battle = #{idBattle}</if>
            <if test="jointime != null "> and jointime = #{jointime}</if>
            ${params.dataScope}
        </where>
        order by jointime desc
    </select>

    <select id="selectVimBattleUserDTOList" parameterType="VimBattleUser" resultMap="VimBattleUserDTOResult">
        <include refid="selectVimBattleUserDTOVo"/>
        <where>  
            <if test="idUser != null "> and vbu.id_user = #{idUser}</if>
            <if test="idBattle != null "> and vbu.id_battle = #{idBattle}</if>
            <if test="jointime != null "> and vbu.jointime = #{jointime}</if>
            <if test="params.userNickname != null and params.userNickname != ''">
                and vu.nickname like concat('%', #{params.userNickname}, '%')
            </if>
            <if test="params.userIdentity != null">
                and vu.identity = #{params.userIdentity}
            </if>
            <if test="params.battleState != null">
                and vb.state = #{params.battleState}
            </if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                and vbu.jointime &gt;= unix_timestamp(#{params.beginTime})
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                and vbu.jointime &lt;= unix_timestamp(#{params.endTime})
            </if>
            ${params.dataScope}
        </where>
        order by vbu.jointime desc
    </select>
    
    <select id="selectVimBattleUserById" parameterType="Long" resultMap="VimBattleUserResult">
        <include refid="selectVimBattleUserVo"/>
        where id = #{id}
    </select>

    <select id="selectVimBattleUserByBattleId" parameterType="Long" resultMap="VimBattleUserDTOResult">
        <include refid="selectVimBattleUserDTOVo"/>
        where vbu.id_battle = #{idBattle}
        order by vbu.jointime desc
    </select>

    <select id="selectVimBattleUserByUserId" parameterType="Long" resultMap="VimBattleUserDTOResult">
        <include refid="selectVimBattleUserDTOVo"/>
        where vbu.id_user = #{idUser}
        order by vbu.jointime desc
    </select>

    <select id="selectUserBattleStatistics" parameterType="Long" resultType="map">
        select 
            count(*) as total_battles,
            count(case when vb.state = 1 then 1 end) as waiting_battles,
            count(case when vb.state = 2 then 1 end) as in_progress_battles,
            count(case when vb.state = 3 then 1 end) as finished_battles,
            coalesce(sum(vb.price), 0) as total_amount
        from vim_battle_user vbu
        left join vim_battle vb on vbu.id_battle = vb.id
        where vbu.id_user = #{idUser}
    </select>

    <select id="countUsersByBattleId" parameterType="Long" resultType="int">
        select count(*) from vim_battle_user where id_battle = #{idBattle}
    </select>

    <select id="countByUserIdentity" parameterType="Integer" resultType="int">
        select count(*) 
        from vim_battle_user vbu
        left join vim_user vu on vbu.id_user = vu.id
        where vu.identity = #{identity}
    </select>

    <select id="selectVimBattleUserByTimeRange" resultMap="VimBattleUserDTOResult">
        <include refid="selectVimBattleUserDTOVo"/>
        where vbu.jointime between #{startTime} and #{endTime}
        order by vbu.jointime desc
    </select>

    <select id="checkUserInBattle" resultType="int">
        select count(*) from vim_battle_user 
        where id_battle = #{idBattle} and id_user = #{idUser}
    </select>

    <select id="getUserIdentityStatistics" resultType="map">
        select 
            'normal_users' as stat_key, 
            count(case when vu.identity = 1 then 1 end) as stat_value
        from vim_battle_user vbu
        left join vim_user vu on vbu.id_user = vu.id
        union all
        select 
            'online_streamers' as stat_key, 
            count(case when vu.identity = 2 then 1 end) as stat_value
        from vim_battle_user vbu
        left join vim_user vu on vbu.id_user = vu.id
        union all
        select 
            'offline_streamers' as stat_key, 
            count(case when vu.identity = 3 then 1 end) as stat_value
        from vim_battle_user vbu
        left join vim_user vu on vbu.id_user = vu.id
        union all
        select 
            'total_users' as stat_key, 
            count(distinct vbu.id_user) as stat_value
        from vim_battle_user vbu
        left join vim_user vu on vbu.id_user = vu.id
    </select>

    <insert id="insertVimBattleUser" parameterType="VimBattleUser" useGeneratedKeys="true" keyProperty="id">
        insert into vim_battle_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="idUser != null">id_user,</if>
            <if test="idBattle != null">id_battle,</if>
            <if test="jointime != null">jointime,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="idUser != null">#{idUser},</if>
            <if test="idBattle != null">#{idBattle},</if>
            <if test="jointime != null">#{jointime},</if>
         </trim>
    </insert>

    <update id="updateVimBattleUser" parameterType="VimBattleUser">
        update vim_battle_user
        <trim prefix="SET" suffixOverrides=",">
            <if test="idUser != null">id_user = #{idUser},</if>
            <if test="idBattle != null">id_battle = #{idBattle},</if>
            <if test="jointime != null">jointime = #{jointime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteVimBattleUserById" parameterType="Long">
        delete from vim_battle_user where id = #{id}
    </delete>

    <delete id="deleteVimBattleUserByIds" parameterType="String">
        delete from vim_battle_user where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteVimBattleUserByBattleId" parameterType="Long">
        delete from vim_battle_user where id_battle = #{idBattle}
    </delete>

</mapper>
