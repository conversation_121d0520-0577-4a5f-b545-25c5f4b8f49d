<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.vimBattleSys.mapper.VimBattleBoxMapper">
    
    <resultMap type="VimBattleBox" id="VimBattleBoxResult">
        <result property="id"    column="id"    />
        <result property="idBattle"    column="id_battle"    />
        <result property="idBox"    column="id_box"    />
        <result property="boxImage"    column="box_image"    />
        <result property="boxItem"    column="box_item"    />
        <result property="boxName"    column="box_name"    />
    </resultMap>

    <sql id="selectVimBattleBoxVo">
        select id, id_battle, id_box, box_image, box_item, box_name from vim_battle_box
    </sql>

    <select id="selectVimBattleBoxList" parameterType="VimBattleBox" resultMap="VimBattleBoxResult">
        <include refid="selectVimBattleBoxVo"/>
        <where>  
            <if test="idBattle != null "> and id_battle = #{idBattle}</if>
            <if test="idBox != null "> and id_box = #{idBox}</if>
            <if test="boxImage != null  and boxImage != ''"> and box_image = #{boxImage}</if>
            <if test="boxItem != null  and boxItem != ''"> and box_item = #{boxItem}</if>
            <if test="boxName != null  and boxName != ''"> and box_name like concat('%', #{boxName}, '%')</if>
        </where>
        order by id desc
    </select>
    
    <select id="selectVimBattleBoxById" parameterType="Long" resultMap="VimBattleBoxResult">
        <include refid="selectVimBattleBoxVo"/>
        where id = #{id}
    </select>

    <select id="selectVimBattleBoxByBattleId" parameterType="Long" resultMap="VimBattleBoxResult">
        <include refid="selectVimBattleBoxVo"/>
        where id_battle = #{idBattle}
        order by id desc
    </select>

    <select id="selectVimBattleBoxByBoxId" parameterType="Long" resultMap="VimBattleBoxResult">
        <include refid="selectVimBattleBoxVo"/>
        where id_box = #{idBox}
        order by id desc
    </select>

    <insert id="insertVimBattleBox" parameterType="VimBattleBox" useGeneratedKeys="true" keyProperty="id">
        insert into vim_battle_box
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="idBattle != null">id_battle,</if>
            <if test="idBox != null">id_box,</if>
            <if test="boxImage != null and boxImage != ''">box_image,</if>
            <if test="boxItem != null and boxItem != ''">box_item,</if>
            <if test="boxName != null and boxName != ''">box_name,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="idBattle != null">#{idBattle},</if>
            <if test="idBox != null">#{idBox},</if>
            <if test="boxImage != null and boxImage != ''">#{boxImage},</if>
            <if test="boxItem != null and boxItem != ''">#{boxItem},</if>
            <if test="boxName != null and boxName != ''">#{boxName},</if>
         </trim>
    </insert>

    <update id="updateVimBattleBox" parameterType="VimBattleBox">
        update vim_battle_box
        <trim prefix="SET" suffixOverrides=",">
            <if test="idBattle != null">id_battle = #{idBattle},</if>
            <if test="idBox != null">id_box = #{idBox},</if>
            <if test="boxImage != null and boxImage != ''">box_image = #{boxImage},</if>
            <if test="boxItem != null and boxItem != ''">box_item = #{boxItem},</if>
            <if test="boxName != null and boxName != ''">box_name = #{boxName},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteVimBattleBoxById" parameterType="Long">
        delete from vim_battle_box where id = #{id}
    </delete>

    <delete id="deleteVimBattleBoxByIds" parameterType="String">
        delete from vim_battle_box where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteVimBattleBoxByBattleId" parameterType="Long">
        delete from vim_battle_box where id_battle = #{idBattle}
    </delete>

    <insert id="batchInsertVimBattleBox" parameterType="java.util.List">
        insert into vim_battle_box (id_battle, id_box, box_image, box_item, box_name)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.idBattle}, #{item.idBox}, #{item.boxImage}, #{item.boxItem}, #{item.boxName})
        </foreach>
    </insert>

</mapper>
