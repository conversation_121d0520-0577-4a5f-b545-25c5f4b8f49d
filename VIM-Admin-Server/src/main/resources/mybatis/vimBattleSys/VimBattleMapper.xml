<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.vimBattleSys.mapper.VimBattleMapper">
    
    <resultMap type="VimBattle" id="VimBattleResult">
        <result property="id"    column="id"    />
        <result property="boxes"    column="boxes"    />
        <result property="mode"    column="mode"    />
        <result property="userCreate"    column="user_create"    />
        <result property="userMax"    column="user_max"    />
        <result property="userNow"    column="user_now"    />
        <result property="price"    column="price"    />
        <result property="state"    column="state"    />
        <result property="battleCreateTime"    column="create_time"    />
        <result property="startTime"    column="start_time"    />
        <result property="seed"    column="seed"    />
        <result property="userWin"    column="user_win"    />
    </resultMap>

    <resultMap type="VimBattleDTO" id="VimBattleDTOResult" extends="VimBattleResult">
        <result property="modeDesc"    column="mode_desc"    />
        <result property="stateDesc"    column="state_desc"    />
        <result property="createUserNickname"    column="create_user_nickname"    />
        <result property="createUserLevel"    column="create_user_level"    />
        <result property="createUserIdentity"    column="create_user_identity"    />
        <result property="createUserIdentityDesc"    column="create_user_identity_desc"    />
        <result property="progressPercent"    column="progress_percent"    />
        <result property="winUserNickname"    column="win_user_nickname"    />
        <result property="winUserLevel"    column="win_user_level"    />
        <result property="winUserIdentity"    column="win_user_identity"    />
        <result property="winUserIdentityDesc"    column="win_user_identity_desc"    />
    </resultMap>

    <sql id="selectVimBattleVo">
        select id, boxes, mode, user_create, user_max, user_now, price, state, create_time, start_time, seed, user_win from vim_battle
    </sql>

    <sql id="selectVimBattleDTOVo">
        select
            vb.id, vb.boxes, vb.mode, vb.user_create, vb.user_max, vb.user_now,
            vb.price, vb.state, vb.create_time, vb.start_time, vb.seed, vb.user_win,
            case vb.mode 
                when 1 then '欧皇'
                when 2 then '非酋'
                when 3 then '天选'
                when 4 then '夺魁'
                else '未知'
            end as mode_desc,
            case vb.state 
                when 1 then '等待中'
                when 2 then '进行中'
                when 3 then '已结束'
                else '未知'
            end as state_desc,
            vu.nickname as create_user_nickname,
            vu.level as create_user_level,
            vu.identity as create_user_identity,
            case vu.identity 
                when 1 then '普通用户'
                when 2 then '线上主播'
                when 3 then '线下主播'
                else '未知'
            end as create_user_identity_desc,
            case when vb.user_max > 0 then round((vb.user_now * 100.0 / vb.user_max), 0) else 0 end as progress_percent,
            vuw.nickname as win_user_nickname,
            vuw.level as win_user_level,
            vuw.identity as win_user_identity,
            case vuw.identity
                when 1 then '普通用户'
                when 2 then '线上主播'
                when 3 then '线下主播'
                else '未知'
            end as win_user_identity_desc
        from vim_battle vb
        left join vim_user vu on vb.user_create = vu.id
        left join vim_user vuw on vb.user_win = vuw.id
    </sql>

    <select id="selectVimBattleList" parameterType="VimBattle" resultMap="VimBattleResult">
        <include refid="selectVimBattleVo"/>
        <where>  
            <if test="boxes != null  and boxes != ''"> and boxes like concat('%', #{boxes}, '%')</if>
            <if test="mode != null "> and mode = #{mode}</if>
            <if test="userCreate != null "> and user_create = #{userCreate}</if>
            <if test="userMax != null "> and user_max = #{userMax}</if>
            <if test="userNow != null "> and user_now = #{userNow}</if>
            <if test="price != null "> and price = #{price}</if>
            <if test="state != null "> and state = #{state}</if>
            <if test="battleCreateTime != null "> and create_time = #{battleCreateTime}</if>
            <if test="startTime != null "> and start_time = #{startTime}</if>
            <if test="seed != null  and seed != ''"> and seed = #{seed}</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                and create_time &gt;= unix_timestamp(#{params.beginTime})
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                and create_time &lt;= unix_timestamp(#{params.endTime})
            </if>
            ${params.dataScope}
        </where>
        order by create_time desc
    </select>

    <select id="selectVimBattleDTOList" parameterType="VimBattle" resultMap="VimBattleDTOResult">
        <include refid="selectVimBattleDTOVo"/>
        <where>
            <if test="id != null "> and vb.id = #{id}</if>
            <if test="boxes != null  and boxes != ''"> and vb.boxes like concat('%', #{boxes}, '%')</if>
            <if test="mode != null "> and vb.mode = #{mode}</if>
            <if test="userCreate != null "> and vb.user_create = #{userCreate}</if>
            <if test="userMax != null "> and vb.user_max = #{userMax}</if>
            <if test="userNow != null "> and vb.user_now = #{userNow}</if>
            <if test="price != null "> and vb.price = #{price}</if>
            <if test="state != null "> and vb.state = #{state}</if>
            <if test="battleCreateTime != null "> and vb.create_time = #{battleCreateTime}</if>
            <if test="startTime != null "> and vb.start_time = #{startTime}</if>
            <if test="seed != null  and seed != ''"> and vb.seed = #{seed}</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                and vb.create_time &gt;= unix_timestamp(#{params.beginTime})
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                and vb.create_time &lt;= unix_timestamp(#{params.endTime})
            </if>
            ${params.dataScope}
        </where>
        order by vb.create_time desc
    </select>
    
    <select id="selectVimBattleById" parameterType="Long" resultMap="VimBattleResult">
        <include refid="selectVimBattleVo"/>
        where id = #{id}
    </select>

    <select id="countVimBattleByState" parameterType="Integer" resultType="int">
        select count(*) from vim_battle where state = #{state}
    </select>

    <select id="countVimBattleByMode" parameterType="Integer" resultType="int">
        select count(*) from vim_battle where mode = #{mode}
    </select>

    <select id="selectVimBattleByTimeRange" resultMap="VimBattleResult">
        <include refid="selectVimBattleVo"/>
        where create_time between #{startTime} and #{endTime}
        order by create_time desc
    </select>

    <select id="selectPopularVimBattle" parameterType="Integer" resultMap="VimBattleResult">
        <include refid="selectVimBattleVo"/>
        where state in (1, 2)
        order by user_now desc, create_time desc
        <if test="limit != null and limit > 0">
            limit #{limit}
        </if>
    </select>

    <select id="selectVimBattleStatistics" resultType="map">
        select 
            'total_battles' as stat_key, count(*) as stat_value from vim_battle
        union all
        select 
            'waiting_battles' as stat_key, count(*) as stat_value from vim_battle where state = 1
        union all
        select 
            'in_progress_battles' as stat_key, count(*) as stat_value from vim_battle where state = 2
        union all
        select 
            'finished_battles' as stat_key, count(*) as stat_value from vim_battle where state = 3
        union all
        select 
            'total_participants' as stat_key, coalesce(sum(user_now), 0) as stat_value from vim_battle
    </select>

    <select id="selectVimBattleByUserCreate" parameterType="Long" resultMap="VimBattleResult">
        <include refid="selectVimBattleVo"/>
        where user_create = #{userCreate}
        order by create_time desc
    </select>

    <insert id="insertVimBattle" parameterType="VimBattle" useGeneratedKeys="true" keyProperty="id">
        insert into vim_battle
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="boxes != null and boxes != ''">boxes,</if>
            <if test="mode != null">mode,</if>
            <if test="userCreate != null">user_create,</if>
            <if test="userMax != null">user_max,</if>
            <if test="userNow != null">user_now,</if>
            <if test="price != null">price,</if>
            <if test="state != null">state,</if>
            <if test="battleCreateTime != null">create_time,</if>
            <if test="startTime != null">start_time,</if>
            <if test="seed != null">seed,</if>
            <if test="userWin != null">user_win,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="boxes != null and boxes != ''">#{boxes},</if>
            <if test="mode != null">#{mode},</if>
            <if test="userCreate != null">#{userCreate},</if>
            <if test="userMax != null">#{userMax},</if>
            <if test="userNow != null">#{userNow},</if>
            <if test="price != null">#{price},</if>
            <if test="state != null">#{state},</if>
            <if test="battleCreateTime != null">#{battleCreateTime},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="seed != null">#{seed},</if>
            <if test="userWin != null">#{userWin},</if>
         </trim>
    </insert>

    <update id="updateVimBattle" parameterType="VimBattle">
        update vim_battle
        <trim prefix="SET" suffixOverrides=",">
            <if test="boxes != null and boxes != ''">boxes = #{boxes},</if>
            <if test="mode != null">mode = #{mode},</if>
            <if test="userCreate != null">user_create = #{userCreate},</if>
            <if test="userMax != null">user_max = #{userMax},</if>
            <if test="userNow != null">user_now = #{userNow},</if>
            <if test="price != null">price = #{price},</if>
            <if test="state != null">state = #{state},</if>
            <if test="battleCreateTime != null">create_time = #{battleCreateTime},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="seed != null">seed = #{seed},</if>
            <if test="userWin != null">user_win = #{userWin},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteVimBattleById" parameterType="Long">
        delete from vim_battle where id = #{id}
    </delete>

    <delete id="deleteVimBattleByIds" parameterType="String">
        delete from vim_battle where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="updateVimBattleUserNow">
        update vim_battle set user_now = #{userNow} where id = #{id}
    </update>

    <update id="updateVimBattleState">
        update vim_battle set state = #{state} where id = #{id}
    </update>

    <update id="updateVimBattleStartTime">
        update vim_battle set start_time = #{startTime} where id = #{id}
    </update>

    <!-- 查询对战房间的用户和订单一一对应数据 -->
    <select id="selectBattleUserOrdersById" parameterType="Long" resultType="java.util.Map">
        select vbo.uid as userId,
               vu.nickname as userName,
               vu.userimage as userAvatar,
               vbo.id as orderId,
               vbo.itemid as itemId,
               vi.name as itemName,
               vi.image as itemImage,
               vi.price_show as itemPrice,
               vbo.itemprice as orderPrice,
               vbu.jointime as joinTime
        from vim_battle_order vbo
        left join vim_user vu on vbo.uid = vu.id
        left join vim_item vi on vbo.itemid = vi.id
        left join vim_battle_user vbu on vbo.bid = vbu.id_battle and vbo.uid = vbu.id_user
        where vbo.bid = #{id}
        order by vbu.jointime asc
    </select>

</mapper>
