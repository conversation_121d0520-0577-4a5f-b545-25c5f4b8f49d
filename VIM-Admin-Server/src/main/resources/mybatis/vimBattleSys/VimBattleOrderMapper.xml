<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.vimBattleSys.mapper.VimBattleOrderMapper">
    
    <resultMap type="VimBattleOrder" id="VimBattleOrderResult">
        <result property="id"    column="id"    />
        <result property="oid"    column="oid"    />
        <result property="uid"    column="uid"    />
        <result property="itemid"    column="itemid"    />
        <result property="itemprice"    column="itemprice"    />
        <result property="bid"    column="bid"    />
    </resultMap>

    <resultMap type="VimBattleOrderDTO" id="VimBattleOrderDTOResult" extends="VimBattleOrderResult">
        <result property="userNickname"    column="user_nickname"    />
        <result property="userLevel"    column="user_level"    />
        <result property="userIdentity"    column="user_identity"    />
        <result property="userIdentityDesc"    column="user_identity_desc"    />
        <result property="battleState"    column="battle_state"    />
        <result property="battleStateDesc"    column="battle_state_desc"    />
        <result property="battlePrice"    column="battle_price"    />
        <result property="battleMode"    column="battle_mode"    />
        <result property="battleModeDesc"    column="battle_mode_desc"    />
        <result property="battleUserMax"    column="battle_user_max"    />
        <result property="battleUserNow"    column="battle_user_now"    />
        <result property="itemName"    column="item_name"    />
        <result property="itemImage"    column="item_image"    />
        <result property="itemLevel"    column="item_level"    />
        <result property="orderCreateTime"    column="order_create_time"    />
        <result property="orderStatus"    column="order_status"    />
        <result property="orderStatusDesc"    column="order_status_desc"    />
    </resultMap>

    <sql id="selectVimBattleOrderVo">
        select id, oid, uid, itemid, itemprice, bid from vim_battle_order
    </sql>

    <sql id="selectVimBattleOrderDTOVo">
        select 
            vbo.id, vbo.oid, vbo.uid, vbo.itemid, vbo.itemprice, vbo.bid,
            vu.nickname as user_nickname,
            vu.level as user_level,
            vu.identity as user_identity,
            case vu.identity 
                when 1 then '普通用户'
                when 2 then '线上主播'
                when 3 then '线下主播'
                else '未知'
            end as user_identity_desc,
            vb.state as battle_state,
            case vb.state 
                when 1 then '等待中'
                when 2 then '进行中'
                when 3 then '已结束'
                else '未知'
            end as battle_state_desc,
            vb.price as battle_price,
            vb.mode as battle_mode,
            case vb.mode 
                when 1 then '欧皇'
                when 2 then '非酋'
                when 3 then '天选'
                when 4 then '夺魁'
                else '未知'
            end as battle_mode_desc,
            vb.user_max as battle_user_max,
            vb.user_now as battle_user_now,
            vi.name as item_name,
            vi.image as item_image,
            COALESCE(vbi.level, 1) as item_level,
            UNIX_TIMESTAMP() as order_create_time,
            1 as order_status,
            '已完成' as order_status_desc
        from vim_battle_order vbo
        left join vim_user vu on vbo.uid = vu.id
        left join vim_battle vb on vbo.bid = vb.id
        left join vim_item vi on vbo.itemid = vi.id
        left join vim_box_item vbi on vi.id = vbi.id_item
    </sql>

    <select id="selectVimBattleOrderList" parameterType="VimBattleOrder" resultMap="VimBattleOrderResult">
        <include refid="selectVimBattleOrderVo"/>
        <where>  
            <if test="oid != null  and oid != ''"> and oid = #{oid}</if>
            <if test="uid != null "> and uid = #{uid}</if>
            <if test="itemid != null "> and itemid = #{itemid}</if>
            <if test="itemprice != null "> and itemprice = #{itemprice}</if>
            <if test="bid != null "> and bid = #{bid}</if>
            ${params.dataScope}
        </where>
        order by id desc
    </select>

    <select id="selectVimBattleOrderDTOList" parameterType="VimBattleOrder" resultMap="VimBattleOrderDTOResult">
        <include refid="selectVimBattleOrderDTOVo"/>
        <where>  
            <if test="oid != null  and oid != ''"> and vbo.oid = #{oid}</if>
            <if test="uid != null "> and vbo.uid = #{uid}</if>
            <if test="itemid != null "> and vbo.itemid = #{itemid}</if>
            <if test="itemprice != null "> and vbo.itemprice = #{itemprice}</if>
            <if test="bid != null "> and vbo.bid = #{bid}</if>
            <if test="params.userNickname != null and params.userNickname != ''">
                and vu.nickname like concat('%', #{params.userNickname}, '%')
            </if>
            <if test="params.userIdentity != null">
                and vu.identity = #{params.userIdentity}
            </if>
            <if test="params.battleState != null">
                and vb.state = #{params.battleState}
            </if>
            ${params.dataScope}
        </where>
        order by vbo.id desc
    </select>
    
    <select id="selectVimBattleOrderById" parameterType="Long" resultMap="VimBattleOrderResult">
        <include refid="selectVimBattleOrderVo"/>
        where id = #{id}
    </select>

    <select id="selectVimBattleOrderByBattleId" parameterType="Long" resultMap="VimBattleOrderDTOResult">
        <include refid="selectVimBattleOrderDTOVo"/>
        where vbo.bid = #{bid}
        order by vbo.id desc
    </select>

    <select id="selectVimBattleOrderByUserId" parameterType="Long" resultMap="VimBattleOrderDTOResult">
        <include refid="selectVimBattleOrderDTOVo"/>
        where vbo.uid = #{uid}
        order by vbo.id desc
    </select>

    <select id="selectVimBattleOrderByOid" parameterType="String" resultMap="VimBattleOrderDTOResult">
        <include refid="selectVimBattleOrderDTOVo"/>
        where vbo.oid = #{oid}
    </select>

    <select id="selectOrderAmountStatistics" resultType="map">
        select 
            count(*) as total_orders,
            coalesce(sum(vbo.itemprice), 0) as total_amount,
            coalesce(avg(vbo.itemprice), 0) as avg_amount,
            coalesce(max(vbo.itemprice), 0) as max_amount,
            coalesce(min(vbo.itemprice), 0) as min_amount
        from vim_battle_order vbo
    </select>

    <select id="sumOrderAmountByBattleId" parameterType="Long" resultType="BigDecimal">
        select coalesce(sum(itemprice), 0) from vim_battle_order where bid = #{bid}
    </select>

    <select id="sumOrderAmountByUserId" parameterType="Long" resultType="BigDecimal">
        select coalesce(sum(itemprice), 0) from vim_battle_order where uid = #{uid}
    </select>

    <select id="selectVimBattleOrderByTimeRange" resultMap="VimBattleOrderDTOResult">
        <include refid="selectVimBattleOrderDTOVo"/>
        order by vbo.id desc
    </select>

    <select id="selectOrderAmountByDate" resultType="map">
        select
            CURDATE() as order_date,
            count(*) as order_count,
            coalesce(sum(vbo.itemprice), 0) as total_amount
        from vim_battle_order vbo
        group by CURDATE()
        order by order_date desc
    </select>

    <select id="getUserConsumptionRanking" resultType="map">
        select 
            vbo.uid as user_id,
            vu.nickname as user_nickname,
            vu.level as user_level,
            vu.identity as user_identity,
            count(*) as order_count,
            coalesce(sum(vbo.itemprice), 0) as total_amount
        from vim_battle_order vbo
        left join vim_user vu on vbo.uid = vu.id
        group by vbo.uid, vu.nickname, vu.level, vu.identity
        order by total_amount desc
        <if test="limit != null and limit > 0">
            limit #{limit}
        </if>
    </select>

    <insert id="insertVimBattleOrder" parameterType="VimBattleOrder" useGeneratedKeys="true" keyProperty="id">
        insert into vim_battle_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="oid != null and oid != ''">oid,</if>
            <if test="uid != null">uid,</if>
            <if test="itemid != null">itemid,</if>
            <if test="itemprice != null">itemprice,</if>
            <if test="bid != null">bid,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="oid != null and oid != ''">#{oid},</if>
            <if test="uid != null">#{uid},</if>
            <if test="itemid != null">#{itemid},</if>
            <if test="itemprice != null">#{itemprice},</if>
            <if test="bid != null">#{bid},</if>
         </trim>
    </insert>

    <update id="updateVimBattleOrder" parameterType="VimBattleOrder">
        update vim_battle_order
        <trim prefix="SET" suffixOverrides=",">
            <if test="oid != null and oid != ''">oid = #{oid},</if>
            <if test="uid != null">uid = #{uid},</if>
            <if test="itemid != null">itemid = #{itemid},</if>
            <if test="itemprice != null">itemprice = #{itemprice},</if>
            <if test="bid != null">bid = #{bid},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteVimBattleOrderById" parameterType="Long">
        delete from vim_battle_order where id = #{id}
    </delete>

    <delete id="deleteVimBattleOrderByIds" parameterType="String">
        delete from vim_battle_order where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteVimBattleOrderByBattleId" parameterType="Long">
        delete from vim_battle_order where bid = #{bid}
    </delete>

</mapper>
