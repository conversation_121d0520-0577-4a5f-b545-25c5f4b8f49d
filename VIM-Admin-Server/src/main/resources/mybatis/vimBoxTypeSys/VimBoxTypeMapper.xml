<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.vimBoxTypeSys.mapper.VimBoxTypeMapper">
    
    <resultMap type="VimBoxType" id="VimBoxTypeResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="sort"    column="sort"    />
        <result property="VimBoxTypecreateTime"    column="create_time"    />
        <result property="VimBoxTypeupdateTime"    column="update_time"    />
        <result property="VimBoxTypedeleteTime"    column="delete_time"    />
    </resultMap>

    <sql id="selectVimBoxTypeVo">
        select id, name, sort, battle, open, create_time, update_time, delete_time from vim_box_type
    </sql>

    <select id="selectVimBoxTypeList" parameterType="VimBoxType" resultMap="VimBoxTypeResult">
        <include refid="selectVimBoxTypeVo"/>
        <where>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="battle != null"> and battle = #{battle}</if>
            <if test="open != null"> and open = #{open}</if>
            and delete_time is  null ORDER BY sort DESC
        </where>

    </select>
    
    <select id="selectVimBoxTypeById" parameterType="Long" resultMap="VimBoxTypeResult">
        <include refid="selectVimBoxTypeVo"/>
        where id = #{id}
    </select>

    <insert id="insertVimBoxType" parameterType="VimBoxType" useGeneratedKeys="true" keyProperty="id">
        insert into vim_box_type
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="sort != null">sort,</if>
            <if test="battle != null">battle,</if>
            <if test="open != null">open,</if>
            <if test="VimBoxTypecreateTime != null">create_time,</if>
            <if test="VimBoxTypeupdateTime != null">update_time,</if>
            <if test="VimBoxTypedeleteTime != null">delete_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="sort != null">#{sort},</if>
            <if test="battle != null">#{battle},</if>
            <if test="open != null">#{open},</if>
            <if test="VimBoxTypecreateTime != null">#{VimBoxTypecreateTime},</if>
            <if test="VimBoxTypeupdateTime != null">#{VimBoxTypeupdateTime},</if>
            <if test="VimBoxTypedeleteTime != null">#{VimBoxTypedeleteTime},</if>
         </trim>
    </insert>

    <update id="updateVimBoxType" parameterType="VimBoxType">
        update vim_box_type
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="battle != null">battle = #{battle},</if>
            <if test="open != null">open = #{open},</if>
            <if test="VimBoxTypecreateTime != null">create_time = #{VimBoxTypecreateTime},</if>
            <if test="VimBoxTypeupdateTime != null">update_time = #{VimBoxTypeupdateTime},</if>
            <if test="VimBoxTypedeleteTime != null">delete_time = #{VimBoxTypedeleteTime},</if>
        </trim>
        where id = #{id}
    </update>

<!--    <delete id="deleteVimBoxTypeById" parameterType="Long">-->
<!--        delete from vim_box_type where id = #{id}-->
<!--    </delete>-->

<!--    <delete id="deleteVimBoxTypeByIds" parameterType="String">-->
<!--        delete from vim_box_type where id in -->
<!--        <foreach item="id" collection="array" open="(" separator="," close=")">-->
<!--            #{id}-->
<!--        </foreach>-->
<!--    </delete>-->
<!--    <update id="deleteVimBoxTypeById" parameterType="java.util.Map">-->
<!--        update vim_box_type-->
<!--        set VimBoxTypedeleteTime = #{deleteTime}-->
<!--        where id = #{id}-->
<!--    </update>-->

<!--    <update id="deleteVimBoxTypeByIds" parameterType="java.util.Map">-->
<!--        update vim_box_type-->
<!--        set VimBoxTypedeleteTime = #{deleteTime}-->
<!--        where id in-->
<!--        <foreach item="id" collection="ids" open="(" separator="," close=")">-->
<!--            #{id}-->
<!--        </foreach>-->
<!--    </update>-->

    <update id="deleteVimBoxTypeById" parameterType="java.util.Map">
        update vim_box_type
        set delete_time = #{deleteTime}
        where id = #{id}
    </update>

    <update id="deleteVimBoxTypeByIds" parameterType="java.util.Map">
        update vim_box_type
        set delete_time = #{deleteTime}
        where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>