<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.VimBlackItemSys.mapper.VimBlackItemMapper">
    
    <resultMap type="VimBlackItem" id="VimBlackItemResult">
        <result property="id"    column="id"    />
        <result property="itemid"    column="itemid"    />
        <result property="level"    column="level"    />
        <result property="left"    column="left"    />
        <result property="all"    column="all"    />
        <result property="last"    column="last"    />
    </resultMap>

<!--    <resultMap type="VimBlackItemAndVimItemDto" id="VimBlackItemAndVimItemResult">-->
<!--        <result property="id" column="id" />-->
<!--        <result property="itemid" column="itemid" />-->
<!--        <result property="level" column="level" />-->
<!--        <result property="left" column="left" />-->
<!--        <result property="all" column="all" />-->
<!--        <result property="last" column="last" />-->
<!--        <result property="vimItemId" column="vim_item_id" />-->
<!--    </resultMap>-->

    <resultMap type="com.ruoyi.project.VimBlackItemSys.domain.VimBlackItemAndVimItemDto" id="VimBlackItemAndVimItemDtoResult">
        <!-- 映射 vimItem 对象 -->
        <association property="vimItem" javaType="com.ruoyi.project.commoditySys.domain.VimItem">
            <result property="id" column="vi_id" />
            <result property="name" column="vi_name" />
            <result property="hashname" column="vi_hashname" />
            <result property="priceShow" column="vi_priceShow" />
            <result property="image" column="vi_image" />
        </association>

        <association property="vimBlackItem" javaType="com.ruoyi.project.VimBlackItemSys.domain.VimBlackItem">
            <result property="id" column="vbi_id" />
            <result property="itemid" column="vbi_itemid" />
            <result property="level" column="vbi_level" />
            <result property="left" column="vbi_left" />
            <result property="all" column="vbi_all" />
            <result property="last" column="vbi_last" />
        </association>
    </resultMap>

    <sql id="selectVimBlackItemVo">
        SELECT id, itemid, `level`, `left`, `all`, LAST FROM vim_black_item
    </sql>

    <sql id="selectVimBlackItemAndVimItemVo">
        SELECT
            vbi.id as vbi_id,
            vbi.itemid as vbi_itemid,
            vbi.level as vbi_level,
            vbi.`left` as vbi_left,
            vbi.`all` as vbi_all,
            vbi.`last` as vbi_last,
            vi.id as vi_id,
            vi.name as vi_name,
            vi.hashname as vi_hashname,
            vi.price_show as vi_priceShow,
            vi.image as vi_image
        FROM
            vim_black_item vbi
                JOIN
            vim_item vi ON vbi.itemid = vi.id
    </sql>

    <select id="selectVimBlackItemAndVimItemList" parameterType="VimBlackItem" resultMap="VimBlackItemAndVimItemDtoResult">
        <include refid="selectVimBlackItemAndVimItemVo" />
        <where>
            <if test="itemid != null">
                AND vbi.itemid = #{itemid}
            </if>
            <if test="level != null">
                AND vbi.level = #{level}
            </if>
            <if test="left != null">
                AND vbi.`left` = #{left}
            </if>
            <if test="all != null">
                AND vbi.`all` = #{all}
            </if>
            <if test="last != null">
                AND vbi.`last` = #{last}
            </if>
        </where>
        ORDER BY vbi.level ASC, vbi.id ASC
    </select>



    <select id="selectVimBlackItemList" parameterType="VimBlackItem" resultMap="VimBlackItemResult">
        <include refid="selectVimBlackItemVo"/>
        <where>  
            <if test="itemid != null "> and itemid = #{itemid}</if>
            <if test="level != null "> and `level` = #{level}</if>
            <if test="left != null "> and `left` = #{left}</if>
            <if test="all != null "> and `all` = #{all}</if>
            <if test="last != null "> and last = #{last}</if>
        </where>
    </select>
    
    <select id="selectVimBlackItemById" parameterType="Long" resultMap="VimBlackItemResult">
        <include refid="selectVimBlackItemVo"/>
        where id = #{id}
    </select>

    <insert id="insertVimBlackItem" parameterType="VimBlackItem" useGeneratedKeys="true" keyProperty="id">
        insert into vim_black_item
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="itemid != null">itemid,</if>
            <if test="level != null">`level`,</if>
            <if test="left != null">`left`,</if>
            <if test="all != null">`all`,</if>
            <if test="last != null">last,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="itemid != null">#{itemid},</if>
            <if test="level != null">#{level},</if>
            <if test="left != null">#{left},</if>
            <if test="all != null">#{all},</if>
            <if test="last != null">#{last},</if>
         </trim>
    </insert>

    <update id="updateVimBlackItem" parameterType="VimBlackItem">
        update vim_black_item
        <trim prefix="SET" suffixOverrides=",">
            <if test="itemid != null">itemid = #{itemid},</if>
            <if test="level != null">`level` = #{level},</if>
            <if test="left != null">`left` = #{left},</if>
            <if test="all != null">`all` = #{all},</if>
            <if test="last != null">last = #{last},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteVimBlackItemById" parameterType="Long">
        delete from vim_black_item where id = #{id}
    </delete>

    <delete id="deleteVimBlackItemByIds" parameterType="String">
        delete from vim_black_item where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>