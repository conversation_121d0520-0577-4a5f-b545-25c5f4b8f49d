<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.VimBoxItemSys.mapper.VimBoxItemMapper">
    
    <resultMap type="VimBoxItem" id="VimBoxItemResult">
        <result property="id"    column="id"    />
        <result property="idItem"    column="id_item"    />
        <result property="idBox"    column="id_box"    />
        <result property="probability"    column="probability"    />
        <result property="level"    column="level"    />
        <result property="VimBoxItemcreateTime"    column="create_time"    />
        <result property="VimBoxItemupdateTime"    column="update_time"    />

    </resultMap>
    <resultMap type="com.ruoyi.project.VimBoxItemSys.domain.VimItemAndVimBoxItemDto" id="VimItemAndVimBoxItemDtoResult">
        <!-- 映射 vimItem 对象 -->
        <association property="vimItem" javaType="com.ruoyi.project.commoditySys.domain.VimItem">
            <result property="id" column="vi_id" />
            <result property="name" column="vi_name" />
            <result property="hashname" column="vi_hashname" />
            <result property="priceShow" column="vi_priceShow" />
            <result property="priceCost" column="vi_priceCost" />
            <result property="priceRecycle" column="vi_priceRecycle" />
            <result property="image" column="vi_image" />
        </association>
        <!-- 映射 VimBoxItem 对象 -->
        <association property="vimBoxItem" javaType="com.ruoyi.project.VimBoxItemSys.domain.VimBoxItem">
            <result property="id" column="vbi_id" />
            <result property="idItem" column="vbi_idItem" />
            <result property="level" column="vbi_level" />
            <result property="idBox" column="vbi_idBox" />
            <result property="probability" column="vbi_probability" />
            <result property="VimBoxItemcreateTime" column="vbi_VimBoxItemcreateTime" />
            <result property="VimBoxItemupdateTime" column="vbi_VimBoxItemupdateTime" />
        </association>
    </resultMap>
    <sql id="selectVimBoxItemVo">
        select id, id_item, id_box, probability, level, create_time, update_time from vim_box_item
    </sql>

    <select id="selectVimBoxItemList" parameterType="VimBoxItem" resultMap="VimBoxItemResult">
        <include refid="selectVimBoxItemVo"/>
        <where>

        </where>

    </select>
    
    <select id="selectVimBoxItemById" parameterType="Long" resultMap="VimBoxItemResult">
        <include refid="selectVimBoxItemVo"/>
        where id = #{id}
    </select>

    <insert id="insertVimBoxItem" parameterType="VimBoxItem" useGeneratedKeys="true" keyProperty="id">
        insert into vim_box_item
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="idItem != null">id_item,</if>
            <if test="idBox != null">id_box,</if>
            <if test="probability != null">probability,</if>
            <if test="level != null">level,</if>
            <if test="VimBoxItemcreateTime != null">create_time,</if>
            <if test="VimBoxItemupdateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="idItem != null">#{idItem},</if>
            <if test="idBox != null">#{idBox},</if>
            <if test="probability != null">#{probability},</if>
            <if test="level != null">#{level},</if>
            <if test="VimBoxItemcreateTime != null">#{VimBoxItemcreateTime},</if>
            <if test="VimBoxItemupdateTime != null">#{VimBoxItemupdateTime},</if>
         </trim>
    </insert>

    <update id="updateVimBoxItem" parameterType="VimBoxItem">
        update vim_box_item
        <trim prefix="SET" suffixOverrides=",">
            <if test="idItem != null">id_item = #{idItem},</if>
            <if test="idBox != null">id_box = #{idBox},</if>
            <if test="probability != null">probability = #{probability},</if>
            <if test="level != null">level = #{level},</if>
            <if test="VimBoxItemcreateTime != null">create_time = #{VimBoxItemcreateTime},</if>
            <if test="VimBoxItemupdateTime != null">update_time = #{VimBoxItemupdateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteVimBoxItemById" parameterType="Long">
        delete from vim_box_item where id = #{id}
    </delete>

    <delete id="deleteVimBoxItemByIds" parameterType="String">
        delete from vim_box_item where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

<!--    <select id="selectVimItemAndVimBoxItemByBoxId" parameterType="Long" resultMap="VimItemAndVimBoxItemDtoResult">-->
<!--        SELECT-->
<!--            vi.id as vi_id,-->
<!--            vi.name as vi_name,-->
<!--            vbi.id as vbi_id,-->
<!--            vbi.id_item as vbi_id_item-->
<!--        FROM vim_item vi-->
<!--                 JOIN vim_box_item vbi ON vi.id = vbi.id_item-->
<!--        WHERE vbi.id_box = #{idBox}-->
<!--    </select>-->
    <select id="selectVimItemAndVimBoxItemByBoxId" parameterType="Long" resultMap="VimItemAndVimBoxItemDtoResult">
        SELECT
            vi.id as vi_id,
            vi.name as vi_name,
            vi.hashname as vi_hashname,
            vi.price_show as vi_priceShow,
            vi.price_cost as vi_priceCost,
            vi.price_recycle as vi_priceRecycle,
            vi.image as vi_image,
            vbi.id as vbi_id,
            vbi.id_item as vbi_idItem,
            vbi.id_box as vbi_idBox,
            vbi.probability as vbi_probability,
            vbi.level as vbi_level,
            vbi.create_time as vbi_VimBoxItemcreateTime,
            vbi.update_time as vbi_VimBoxItemupdateTime
        FROM vim_item vi
                 JOIN vim_box_item vbi ON vi.id = vbi.id_item
        WHERE vbi.id_box = #{idBox}
        order by vbi.id
    </select>

</mapper>