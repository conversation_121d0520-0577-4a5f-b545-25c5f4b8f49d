<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.permissionAudit.mapper.PermissionAuditTaskMapper">
    
    <resultMap type="PermissionAuditTask" id="PermissionAuditTaskResult">
        <result property="taskId"                    column="task_id"                    />
        <result property="taskName"                 column="task_name"                  />
        <result property="description"              column="description"                />
        <result property="status"                   column="status"                     />
        <result property="scanScopes"               column="scan_scopes"                />
        <result property="scanConfig"               column="scan_config"                />
        <result property="startTime"                column="start_time"                 />
        <result property="endTime"                  column="end_time"                   />
        <result property="duration"                 column="duration"                   />
        <result property="progress"                 column="progress"                   />
        <result property="frontendPermissionCount"  column="frontend_permission_count"  />
        <result property="backendPermissionCount"   column="backend_permission_count"   />
        <result property="databasePermissionCount"  column="database_permission_count"  />
        <result property="missingPermissionCount"   column="missing_permission_count"   />
        <result property="extraPermissionCount"     column="extra_permission_count"     />
        <result property="consistencyScore"         column="consistency_score"          />
        <result property="hasRepairScript"          column="has_repair_script"          />
        <result property="errorMessage"             column="error_message"              />
        <result property="result"                   column="result"                     />
        <result property="createBy"                 column="create_by"                  />
        <result property="createTime"               column="create_time"                />
        <result property="updateBy"                 column="update_by"                  />
        <result property="updateTime"               column="update_time"                />
        <result property="remark"                   column="remark"                     />
    </resultMap>

    <sql id="selectPermissionAuditTaskVo">
        select task_id, task_name, description, status, scan_scopes, scan_config, start_time, end_time, duration, progress,
               frontend_permission_count, backend_permission_count, database_permission_count, 
               missing_permission_count, extra_permission_count, consistency_score, has_repair_script,
               error_message, result, create_by, create_time, update_by, update_time, remark
        from permission_audit_task
    </sql>

    <select id="selectAuditTaskList" parameterType="PermissionAuditTask" resultMap="PermissionAuditTaskResult">
        <include refid="selectPermissionAuditTaskVo"/>
        <where>  
            <if test="taskName != null  and taskName != ''"> and task_name like concat('%', #{taskName}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                and date_format(create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                and date_format(create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectAuditTaskById" parameterType="Long" resultMap="PermissionAuditTaskResult">
        <include refid="selectPermissionAuditTaskVo"/>
        where task_id = #{taskId}
    </select>
        
    <insert id="insertAuditTask" parameterType="PermissionAuditTask" useGeneratedKeys="true" keyProperty="taskId">
        insert into permission_audit_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskName != null and taskName != ''">task_name,</if>
            <if test="description != null">description,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="scanScopes != null">scan_scopes,</if>
            <if test="scanConfig != null">scan_config,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="duration != null">duration,</if>
            <if test="progress != null">progress,</if>
            <if test="frontendPermissionCount != null">frontend_permission_count,</if>
            <if test="backendPermissionCount != null">backend_permission_count,</if>
            <if test="databasePermissionCount != null">database_permission_count,</if>
            <if test="missingPermissionCount != null">missing_permission_count,</if>
            <if test="extraPermissionCount != null">extra_permission_count,</if>
            <if test="consistencyScore != null">consistency_score,</if>
            <if test="hasRepairScript != null">has_repair_script,</if>
            <if test="errorMessage != null">error_message,</if>
            <if test="result != null">result,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskName != null and taskName != ''">#{taskName},</if>
            <if test="description != null">#{description},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="scanScopes != null">#{scanScopes},</if>
            <if test="scanConfig != null">#{scanConfig},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="duration != null">#{duration},</if>
            <if test="progress != null">#{progress},</if>
            <if test="frontendPermissionCount != null">#{frontendPermissionCount},</if>
            <if test="backendPermissionCount != null">#{backendPermissionCount},</if>
            <if test="databasePermissionCount != null">#{databasePermissionCount},</if>
            <if test="missingPermissionCount != null">#{missingPermissionCount},</if>
            <if test="extraPermissionCount != null">#{extraPermissionCount},</if>
            <if test="consistencyScore != null">#{consistencyScore},</if>
            <if test="hasRepairScript != null">#{hasRepairScript},</if>
            <if test="errorMessage != null">#{errorMessage},</if>
            <if test="result != null">#{result},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateAuditTask" parameterType="PermissionAuditTask">
        update permission_audit_task
        <trim prefix="SET" suffixOverrides=",">
            <if test="taskName != null and taskName != ''">task_name = #{taskName},</if>
            <if test="description != null">description = #{description},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="scanScopes != null">scan_scopes = #{scanScopes},</if>
            <if test="scanConfig != null">scan_config = #{scanConfig},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="duration != null">duration = #{duration},</if>
            <if test="progress != null">progress = #{progress},</if>
            <if test="frontendPermissionCount != null">frontend_permission_count = #{frontendPermissionCount},</if>
            <if test="backendPermissionCount != null">backend_permission_count = #{backendPermissionCount},</if>
            <if test="databasePermissionCount != null">database_permission_count = #{databasePermissionCount},</if>
            <if test="missingPermissionCount != null">missing_permission_count = #{missingPermissionCount},</if>
            <if test="extraPermissionCount != null">extra_permission_count = #{extraPermissionCount},</if>
            <if test="consistencyScore != null">consistency_score = #{consistencyScore},</if>
            <if test="hasRepairScript != null">has_repair_script = #{hasRepairScript},</if>
            <if test="errorMessage != null">error_message = #{errorMessage},</if>
            <if test="result != null">result = #{result},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where task_id = #{taskId}
    </update>

    <delete id="deleteAuditTaskById" parameterType="Long">
        delete from permission_audit_task where task_id = #{taskId}
    </delete>

    <delete id="deleteAuditTaskByIds" parameterType="String">
        delete from permission_audit_task where task_id in 
        <foreach item="taskId" collection="array" open="(" separator="," close=")">
            #{taskId}
        </foreach>
    </delete>

    <select id="checkTaskNameUnique" parameterType="String" resultMap="PermissionAuditTaskResult">
        <include refid="selectPermissionAuditTaskVo"/>
        where task_name = #{taskName} limit 1
    </select>

    <delete id="deleteExpiredTasks" parameterType="int">
        delete from permission_audit_task 
        where create_time &lt; date_sub(now(), interval #{days} day)
    </delete>

    <select id="selectTaskStatistics" resultType="java.util.Map">
        select 
            count(*) as total_tasks,
            sum(case when status = 'COMPLETED' then 1 else 0 end) as completed_tasks,
            sum(case when status = 'RUNNING' then 1 else 0 end) as running_tasks,
            sum(case when status = 'FAILED' then 1 else 0 end) as failed_tasks
        from permission_audit_task
    </select>

    <select id="countTasksByStatus" parameterType="String" resultType="int">
        select count(*) from permission_audit_task where status = #{status}
    </select>

    <select id="selectRecentTasks" parameterType="int" resultMap="PermissionAuditTaskResult">
        <include refid="selectPermissionAuditTaskVo"/>
        order by create_time desc
        limit #{limit}
    </select>

</mapper>
