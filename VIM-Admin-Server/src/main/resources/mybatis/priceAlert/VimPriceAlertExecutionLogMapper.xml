<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.priceAlert.mapper.VimPriceAlertExecutionLogMapper">
    
    <resultMap type="VimPriceAlertExecutionLog" id="VimPriceAlertExecutionLogResult">
        <result property="id"                       column="id" />
        <result property="executionType"            column="execution_type" />
        <result property="ruleType"                 column="rule_type" />
        <result property="executionTime"            column="execution_time" />
        <result property="totalItemsChecked"        column="total_items_checked" />
        <result property="triggeredAlerts"          column="triggered_alerts" />
        <result property="executionDuration"        column="execution_duration" />
        <result property="executionStatus"          column="execution_status" />
        <result property="errorMessage"             column="error_message" />
        <result property="executionDetails"         column="execution_details" />
        <result property="triggerBy"                column="trigger_by" />
    </resultMap>

    <sql id="selectVimPriceAlertExecutionLogVo">
        select id, execution_type, rule_type, execution_time, total_items_checked, 
               triggered_alerts, execution_duration, execution_status, error_message, 
               execution_details, trigger_by
        from vim_price_alert_execution_log
    </sql>

    <select id="selectExecutionLogList" parameterType="VimPriceAlertExecutionLog" resultMap="VimPriceAlertExecutionLogResult">
        <include refid="selectVimPriceAlertExecutionLogVo"/>
        <where>  
            <if test="executionType != null and executionType != ''"> and execution_type = #{executionType}</if>
            <if test="ruleType != null and ruleType != ''"> and rule_type = #{ruleType}</if>
            <if test="executionStatus != null "> and execution_status = #{executionStatus}</if>
            <if test="triggerBy != null and triggerBy != ''"> and trigger_by = #{triggerBy}</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                and date_format(execution_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                and date_format(execution_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
        </where>
        order by execution_time desc
    </select>
    
    <select id="selectExecutionLogById" parameterType="Long" resultMap="VimPriceAlertExecutionLogResult">
        <include refid="selectVimPriceAlertExecutionLogVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertExecutionLog" parameterType="VimPriceAlertExecutionLog" useGeneratedKeys="true" keyProperty="id">
        insert into vim_price_alert_execution_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="executionType != null and executionType != ''">execution_type,</if>
            <if test="ruleType != null">rule_type,</if>
            <if test="executionTime != null">execution_time,</if>
            <if test="totalItemsChecked != null">total_items_checked,</if>
            <if test="triggeredAlerts != null">triggered_alerts,</if>
            <if test="executionDuration != null">execution_duration,</if>
            <if test="executionStatus != null">execution_status,</if>
            <if test="errorMessage != null">error_message,</if>
            <if test="executionDetails != null">execution_details,</if>
            <if test="triggerBy != null">trigger_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="executionType != null and executionType != ''">#{executionType},</if>
            <if test="ruleType != null">#{ruleType},</if>
            <if test="executionTime != null">#{executionTime},</if>
            <if test="totalItemsChecked != null">#{totalItemsChecked},</if>
            <if test="triggeredAlerts != null">#{triggeredAlerts},</if>
            <if test="executionDuration != null">#{executionDuration},</if>
            <if test="executionStatus != null">#{executionStatus},</if>
            <if test="errorMessage != null">#{errorMessage},</if>
            <if test="executionDetails != null">#{executionDetails},</if>
            <if test="triggerBy != null">#{triggerBy},</if>
         </trim>
    </insert>

    <update id="updateExecutionLog" parameterType="VimPriceAlertExecutionLog">
        update vim_price_alert_execution_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="executionType != null and executionType != ''">execution_type = #{executionType},</if>
            <if test="ruleType != null">rule_type = #{ruleType},</if>
            <if test="executionTime != null">execution_time = #{executionTime},</if>
            <if test="totalItemsChecked != null">total_items_checked = #{totalItemsChecked},</if>
            <if test="triggeredAlerts != null">triggered_alerts = #{triggeredAlerts},</if>
            <if test="executionDuration != null">execution_duration = #{executionDuration},</if>
            <if test="executionStatus != null">execution_status = #{executionStatus},</if>
            <if test="errorMessage != null">error_message = #{errorMessage},</if>
            <if test="executionDetails != null">execution_details = #{executionDetails},</if>
            <if test="triggerBy != null">trigger_by = #{triggerBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteExecutionLogByIds" parameterType="String">
        delete from vim_price_alert_execution_log where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="cleanExpiredLogs" parameterType="int">
        delete from vim_price_alert_execution_log 
        where execution_time &lt; date_sub(now(), interval #{retentionDays} day)
    </delete>

    <select id="countAllExecutions" resultType="int">
        select count(*) from vim_price_alert_execution_log
    </select>

    <select id="selectLatestExecution" resultMap="VimPriceAlertExecutionLogResult">
        <include refid="selectVimPriceAlertExecutionLogVo"/>
        order by execution_time desc
        limit 1
    </select>

    <select id="selectLogsByExecutionType" parameterType="String" resultMap="VimPriceAlertExecutionLogResult">
        <include refid="selectVimPriceAlertExecutionLogVo"/>
        where execution_type = #{executionType}
        order by execution_time desc
    </select>

    <select id="selectLogsByRuleType" parameterType="String" resultMap="VimPriceAlertExecutionLogResult">
        <include refid="selectVimPriceAlertExecutionLogVo"/>
        where rule_type = #{ruleType}
        order by execution_time desc
    </select>

    <select id="selectLogsByTimeRange" resultMap="VimPriceAlertExecutionLogResult">
        <include refid="selectVimPriceAlertExecutionLogVo"/>
        where execution_time between #{startTime} and #{endTime}
        order by execution_time desc
    </select>

    <select id="getExecutionStatistics" parameterType="int" resultType="java.util.Map">
        select date_format(execution_time,'%Y-%m-%d') as date,
               count(*) as totalExecutions,
               sum(case when execution_status = 1 then 1 else 0 end) as successExecutions,
               sum(case when execution_status = 2 then 1 else 0 end) as partialSuccessExecutions,
               sum(case when execution_status = 3 then 1 else 0 end) as failedExecutions,
               avg(execution_duration) as avgDuration,
               sum(total_items_checked) as totalItemsChecked,
               sum(triggered_alerts) as totalTriggeredAlerts
        from vim_price_alert_execution_log
        where execution_time >= date_sub(now(), interval #{days} day)
        group by date_format(execution_time,'%Y-%m-%d')
        order by date
    </select>

    <select id="getExecutionSuccessRate" parameterType="int" resultType="java.util.Map">
        select 
            count(*) as totalExecutions,
            sum(case when execution_status = 1 then 1 else 0 end) as successExecutions,
            sum(case when execution_status = 2 then 1 else 0 end) as partialSuccessExecutions,
            sum(case when execution_status = 3 then 1 else 0 end) as failedExecutions,
            round(sum(case when execution_status = 1 then 1 else 0 end) * 100.0 / count(*), 2) as successRate
        from vim_price_alert_execution_log
        where execution_time >= date_sub(now(), interval #{days} day)
    </select>

    <select id="getAverageExecutionTime" parameterType="int" resultType="java.util.Map">
        select 
            avg(execution_duration) as avgDuration,
            min(execution_duration) as minDuration,
            max(execution_duration) as maxDuration,
            count(*) as totalExecutions
        from vim_price_alert_execution_log
        where execution_time >= date_sub(now(), interval #{days} day)
          and execution_status in (1, 2)
    </select>

    <select id="getExecutionStatusStatistics" resultType="java.util.Map">
        select execution_status as executionStatus, count(*) as count
        from vim_price_alert_execution_log
        group by execution_status
        order by execution_status
    </select>

    <select id="selectRecentExecutions" parameterType="int" resultMap="VimPriceAlertExecutionLogResult">
        <include refid="selectVimPriceAlertExecutionLogVo"/>
        order by execution_time desc
        limit #{limit}
    </select>

    <select id="countExecutionsByTimeRange" resultType="int">
        select count(*) from vim_price_alert_execution_log
        where execution_time between #{startTime} and #{endTime}
        <if test="executionType != null and executionType != ''">
            and execution_type = #{executionType}
        </if>
    </select>

    <select id="getExecutionEfficiencyStats" parameterType="int" resultType="java.util.Map">
        select 
            date_format(execution_time,'%Y-%m-%d') as date,
            avg(case when execution_duration > 0 then total_items_checked * 1000.0 / execution_duration else 0 end) as avgItemsPerSecond,
            avg(case when total_items_checked > 0 then triggered_alerts * 100.0 / total_items_checked else 0 end) as avgTriggerRate
        from vim_price_alert_execution_log
        where execution_time >= date_sub(now(), interval #{days} day)
          and execution_status in (1, 2)
        group by date_format(execution_time,'%Y-%m-%d')
        order by date
    </select>

    <select id="getErrorLogStatistics" parameterType="int" resultType="java.util.Map">
        select 
            date_format(execution_time,'%Y-%m-%d') as date,
            count(*) as errorCount,
            group_concat(distinct substring(error_message, 1, 100) separator '; ') as errorMessages
        from vim_price_alert_execution_log
        where execution_time >= date_sub(now(), interval #{days} day)
          and execution_status = 3
          and error_message is not null
        group by date_format(execution_time,'%Y-%m-%d')
        order by date
    </select>

    <select id="hasRunningExecution" resultType="boolean">
        select count(*) > 0 from vim_price_alert_execution_log
        where execution_status is null
           or (execution_time >= date_sub(now(), interval 1 hour) and execution_status = 1 and execution_duration is null)
    </select>

    <select id="getExecutionTrendData" parameterType="int" resultType="java.util.Map">
        select 
            date_format(execution_time,'%Y-%m-%d %H:00:00') as hour,
            count(*) as executionCount,
            avg(execution_duration) as avgDuration,
            sum(triggered_alerts) as totalAlerts
        from vim_price_alert_execution_log
        where execution_time >= date_sub(now(), interval #{days} day)
        group by date_format(execution_time,'%Y-%m-%d %H:00:00')
        order by hour
    </select>

</mapper>
