<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.priceAlert.mapper.VimPriceAlertConfigMapper">
    
    <resultMap type="VimPriceAlertConfig" id="VimPriceAlertConfigResult">
        <result property="id"                       column="id" />
        <result property="ruleType"                 column="rule_type" />
        <result property="ruleName"                 column="rule_name" />
        <result property="thresholdType"            column="threshold_type" />
        <result property="thresholdValue"           column="threshold_value" />
        <result property="dataSources"              column="data_sources" />
        <result property="dataSourcePriority"       column="data_source_priority" />
        <result property="minPriceFilter"           column="min_price_filter" />
        <result property="executionTimes"           column="execution_times" />
        <result property="isEnabled"                column="is_enabled" />
        <result property="notificationMethods"      column="notification_methods" />
        <result property="notificationUsers"        column="notification_users" />
        <result property="lastExecutionTime"        column="last_execution_time" />
        <result property="lastTriggerCount"         column="last_trigger_count" />
        <result property="totalTriggerCount"        column="total_trigger_count" />
        <result property="createTime"               column="create_time" />
        <result property="updateTime"               column="update_time" />
        <result property="updateBy"                 column="update_by" />
        <result property="remark"                   column="remark" />
    </resultMap>

    <sql id="selectVimPriceAlertConfigVo">
        select id, rule_type, rule_name, threshold_type, threshold_value, data_sources, 
               data_source_priority, min_price_filter, execution_times, is_enabled, 
               notification_methods, notification_users, last_execution_time, 
               last_trigger_count, total_trigger_count, create_time, update_time, 
               update_by, remark
        from vim_price_alert_config
    </sql>

    <select id="selectAllConfigs" resultMap="VimPriceAlertConfigResult">
        <include refid="selectVimPriceAlertConfigVo"/>
        order by rule_type
    </select>

    <select id="selectConfigByRuleType" parameterType="String" resultMap="VimPriceAlertConfigResult">
        <include refid="selectVimPriceAlertConfigVo"/>
        where rule_type = #{ruleType}
    </select>

    <select id="selectEnabledConfigs" resultMap="VimPriceAlertConfigResult">
        <include refid="selectVimPriceAlertConfigVo"/>
        where is_enabled = 1
        order by rule_type
    </select>

    <insert id="insertConfig" parameterType="VimPriceAlertConfig" useGeneratedKeys="true" keyProperty="id">
        insert into vim_price_alert_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="ruleType != null and ruleType != ''">rule_type,</if>
            <if test="ruleName != null and ruleName != ''">rule_name,</if>
            <if test="thresholdType != null">threshold_type,</if>
            <if test="thresholdValue != null">threshold_value,</if>
            <if test="dataSources != null">data_sources,</if>
            <if test="dataSourcePriority != null">data_source_priority,</if>
            <if test="minPriceFilter != null">min_price_filter,</if>
            <if test="executionTimes != null">execution_times,</if>
            <if test="isEnabled != null">is_enabled,</if>
            <if test="notificationMethods != null">notification_methods,</if>
            <if test="notificationUsers != null">notification_users,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="ruleType != null and ruleType != ''">#{ruleType},</if>
            <if test="ruleName != null and ruleName != ''">#{ruleName},</if>
            <if test="thresholdType != null">#{thresholdType},</if>
            <if test="thresholdValue != null">#{thresholdValue},</if>
            <if test="dataSources != null">#{dataSources},</if>
            <if test="dataSourcePriority != null">#{dataSourcePriority},</if>
            <if test="minPriceFilter != null">#{minPriceFilter},</if>
            <if test="executionTimes != null">#{executionTimes},</if>
            <if test="isEnabled != null">#{isEnabled},</if>
            <if test="notificationMethods != null">#{notificationMethods},</if>
            <if test="notificationUsers != null">#{notificationUsers},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateConfig" parameterType="VimPriceAlertConfig">
        update vim_price_alert_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="ruleName != null and ruleName != ''">rule_name = #{ruleName},</if>
            <if test="thresholdType != null">threshold_type = #{thresholdType},</if>
            <if test="thresholdValue != null">threshold_value = #{thresholdValue},</if>
            <if test="dataSources != null">data_sources = #{dataSources},</if>
            <if test="dataSourcePriority != null">data_source_priority = #{dataSourcePriority},</if>
            <if test="minPriceFilter != null">min_price_filter = #{minPriceFilter},</if>
            <if test="executionTimes != null">execution_times = #{executionTimes},</if>
            <if test="isEnabled != null">is_enabled = #{isEnabled},</if>
            <if test="notificationMethods != null">notification_methods = #{notificationMethods},</if>
            <if test="notificationUsers != null">notification_users = #{notificationUsers},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where rule_type = #{ruleType}
    </update>

    <update id="updateExecutionStats" parameterType="VimPriceAlertConfig">
        update vim_price_alert_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="lastExecutionTime != null">last_execution_time = #{lastExecutionTime},</if>
            <if test="lastTriggerCount != null">last_trigger_count = #{lastTriggerCount},</if>
            <if test="totalTriggerCount != null">total_trigger_count = total_trigger_count + #{lastTriggerCount},</if>
        </trim>
        where rule_type = #{ruleType}
    </update>

    <select id="countAllConfigs" resultType="int">
        select count(*) from vim_price_alert_config
    </select>

    <select id="countEnabledConfigs" resultType="int">
        select count(*) from vim_price_alert_config where is_enabled = 1
    </select>

    <update id="toggleConfigStatus">
        update vim_price_alert_config 
        set is_enabled = #{enabled}, update_time = now()
        where rule_type = #{ruleType}
    </update>

    <update id="batchUpdateConfigs" parameterType="java.util.List">
        <foreach collection="configs" item="config" separator=";">
            update vim_price_alert_config
            <trim prefix="SET" suffixOverrides=",">
                <if test="config.ruleName != null and config.ruleName != ''">rule_name = #{config.ruleName},</if>
                <if test="config.thresholdType != null">threshold_type = #{config.thresholdType},</if>
                <if test="config.thresholdValue != null">threshold_value = #{config.thresholdValue},</if>
                <if test="config.dataSources != null">data_sources = #{config.dataSources},</if>
                <if test="config.dataSourcePriority != null">data_source_priority = #{config.dataSourcePriority},</if>
                <if test="config.minPriceFilter != null">min_price_filter = #{config.minPriceFilter},</if>
                <if test="config.executionTimes != null">execution_times = #{config.executionTimes},</if>
                <if test="config.isEnabled != null">is_enabled = #{config.isEnabled},</if>
                <if test="config.notificationMethods != null">notification_methods = #{config.notificationMethods},</if>
                <if test="config.notificationUsers != null">notification_users = #{config.notificationUsers},</if>
                <if test="config.updateTime != null">update_time = #{config.updateTime},</if>
                <if test="config.updateBy != null">update_by = #{config.updateBy},</if>
                <if test="config.remark != null">remark = #{config.remark},</if>
            </trim>
            where rule_type = #{config.ruleType}
        </foreach>
    </update>

    <update id="resetConfigToDefault">
        update vim_price_alert_config
        set threshold_type = 1,
            threshold_value = case 
                when rule_type = 'price_up' then 15.0000
                when rule_type = 'price_down' then 10.0000
                else threshold_value
            end,
            data_sources = 'buff,steam',
            data_source_priority = 'buff,steam,c5game',
            min_price_filter = case 
                when rule_type = 'price_up' then 50.00
                when rule_type = 'price_down' then 20.00
                else min_price_filter
            end,
            execution_times = '09:00,15:00,21:00',
            is_enabled = 1,
            notification_methods = 'system',
            update_time = now()
        where rule_type = #{ruleType}
    </update>

    <select id="getLastExecutionTime" parameterType="String" resultType="java.util.Date">
        select last_execution_time from vim_price_alert_config where rule_type = #{ruleType}
    </select>

    <select id="existsByRuleType" parameterType="String" resultType="boolean">
        select count(*) > 0 from vim_price_alert_config where rule_type = #{ruleType}
    </select>

</mapper>
