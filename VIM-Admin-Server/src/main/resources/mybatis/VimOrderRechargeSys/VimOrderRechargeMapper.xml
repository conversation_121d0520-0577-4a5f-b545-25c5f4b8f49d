<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.VimOrderRechargeSys.mapper.VimOrderRechargeMapper">
    
    <resultMap type="VimOrderRecharge" id="VimOrderRechargeResult">
        <result property="id"    column="id"    />
        <result property="uid"    column="uid"    />
        <result property="amount"    column="amount"    />
        <result property="coin"    column="coin"    />
        <result property="state"    column="state"    />
        <result property="payid"    column="payid"    />
        <result property="OrderRechargeCreateTime"    column="create_time"    />
        <result property="OrderRechargeUpdateTime"    column="update_time"    />
    </resultMap>

    <!-- 充值订单扩展结果映射，包含用户信息 -->
    <resultMap type="com.ruoyi.project.VimOrderRechargeSys.domain.vo.VimOrderRechargeVO" id="VimOrderRechargeVOResult" extends="VimOrderRechargeResult">
        <result property="userNickname"    column="user_nickname"    />
        <result property="userPhoneRaw"    column="user_phone"    />
    </resultMap>

    <sql id="selectVimOrderRechargeVo">
        select id, uid, amount, coin, state, payid, create_time, update_time from vim_order_recharge
    </sql>

    <!-- 包含用户信息的充值订单查询SQL -->
    <sql id="selectVimOrderRechargeWithUserVo">
        select
            vor.id, vor.uid, vor.amount, vor.coin, vor.state, vor.payid,
            vor.create_time, vor.update_time,
            vu.nickname as user_nickname,
            vu.phone as user_phone
        from vim_order_recharge vor
        left join vim_user vu on vor.uid = vu.id
    </sql>

    <select id="selectVimOrderRechargeList" parameterType="VimOrderRecharge" resultMap="VimOrderRechargeVOResult">
        <include refid="selectVimOrderRechargeWithUserVo"/>
        <where>
            <if test="id != null  and id != ''"> and vor.id = #{id}</if>
            <if test="uid != null "> and vor.uid = #{uid}</if>
            <if test="amount != null "> and vor.amount = #{amount}</if>
            <if test="coin != null "> and vor.coin = #{coin}</if>
            <if test="state != null "> and vor.state = #{state}</if>
            <if test="payid != null and payid != ''"> and vor.payid = #{payid}</if>
        </where>
        ORDER BY vor.create_time DESC
    </select>
    
    <select id="selectVimOrderRechargeById" parameterType="String" resultMap="VimOrderRechargeResult">
        <include refid="selectVimOrderRechargeVo"/>
        where id = #{id}
    </select>

    <insert id="insertVimOrderRecharge" parameterType="VimOrderRecharge">
        insert into vim_order_recharge
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="uid != null">uid,</if>
            <if test="amount != null">amount,</if>
            <if test="coin != null">coin,</if>
            <if test="state != null">state,</if>
            <if test="payid != null">payid,</if>
            <if test="VimOrderRechargescreateTime != null">create_time,</if>
            <if test="VimOrderRechargesupdateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="uid != null">#{uid},</if>
            <if test="amount != null">#{amount},</if>
            <if test="coin != null">#{coin},</if>
            <if test="state != null">#{state},</if>
            <if test="payid != null">#{payid},</if>
            <if test="VimOrderRechargescreateTime != null">#{VimOrderRechargescreateTime},</if>
            <if test="VimOrderRechargesupdateTime != null">#{VimOrderRechargesupdateTime},</if>
         </trim>
    </insert>

    <update id="updateVimOrderRecharge" parameterType="VimOrderRecharge">
        update vim_order_recharge
        <trim prefix="SET" suffixOverrides=",">
            <if test="uid != null">uid = #{uid},</if>
            <if test="amount != null">amount = #{amount},</if>
            <if test="coin != null">coin = #{coin},</if>
            <if test="state != null">state = #{state},</if>
            <if test="payid != null">payid = #{payid},</if>
            <if test="VimOrderRechargescreateTime != null">create_time = #{VimOrderRechargescreateTime},</if>
            <if test="VimOrderRechargesupdateTime != null">update_time = #{VimOrderRechargesupdateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteVimOrderRechargeById" parameterType="String">
        delete from vim_order_recharge where id = #{id}
    </delete>

    <delete id="deleteVimOrderRechargeByIds" parameterType="String">
        delete from vim_order_recharge where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>