<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.vimAnchor.mapper.OrderRechargeMapper">

    <!-- 订单充值记录结果映射 -->
    <resultMap id="OrderRechargeMap" type="com.ruoyi.project.vimAnchor.domain.OrderRecharge">
        <id column="id" property="id" />
        <result column="uid" property="uid" />
        <result column="amount" property="amount" />
        <result column="coin" property="coin" />
        <result column="state" property="state" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>
    
    <!-- 根据ID查询订单充值记录 -->
    <select id="selectOrderRechargeById" resultMap="OrderRechargeMap">
        SELECT 
            id, uid, amount, coin, state, create_time, update_time
        FROM 
            vim_order_recharge
        WHERE 
            id = #{id}
    </select>
    
    <!-- 查询订单充值记录列表 -->
    <select id="selectOrderRechargeList" resultMap="OrderRechargeMap">
        SELECT 
            r.id,
            r.uid,
            u.username,
            u.nickname,
            r.amount,
            r.coin,
            r.state,
            FROM_UNIXTIME(r.create_time, '%Y-%m-%d %H:%i:%s') AS create_time,
            FROM_UNIXTIME(r.update_time, '%Y-%m-%d %H:%i:%s') AS update_time
        FROM 
            vim_order_recharge r
            LEFT JOIN vim_user u ON r.uid = u.id
        WHERE 
            1=1
            <if test="userId != null">
                AND r.uid = #{userId}
            </if>
            <if test="state != null">
                AND r.state = #{state}
            </if>
            <if test="startTime != null">
                AND r.create_time >= #{startTime}
            </if>
            <if test="endTime != null">
                AND r.create_time &lt;= #{endTime}
            </if>
        ORDER BY r.create_time DESC
    </select>
    
    <!-- 查询订单充值记录总数 -->
    <select id="selectOrderRechargeCount" resultType="int">
        SELECT 
            COUNT(*)
        FROM 
            vim_order_recharge
        WHERE 
            1=1
            <if test="uid != null">
                AND uid = #{uid}
            </if>
            <if test="state != null">
                AND state = #{state}
            </if>
            <if test="startTime != null">
                AND create_time >= #{startTime}
            </if>
            <if test="endTime != null">
                AND create_time &lt;= #{endTime}
            </if>
    </select>
    
    <!-- 根据用户ID查询充值总额 -->
    <select id="selectRechargeAmountByUserId" resultType="java.math.BigDecimal">
        SELECT
            COALESCE(SUM(amount), 0)
        FROM
            vim_order_recharge
        WHERE
            uid = #{uid}
            AND state = 2
    </select>
    
    <!-- 根据时间范围查询充值总额 -->
    <select id="selectRechargeAmountByTimeRange" resultType="java.math.BigDecimal">
        SELECT 
            COALESCE(SUM(amount), 0)
        FROM 
            vim_order_recharge
        WHERE 
            state = 1
            <if test="startTime != null">
                AND create_time >= #{startTime}
            </if>
            <if test="endTime != null">
                AND create_time &lt;= #{endTime}
            </if>
    </select>
</mapper> 