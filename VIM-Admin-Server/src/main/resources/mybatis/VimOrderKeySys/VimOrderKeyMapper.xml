<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.VimOrderKeySys.mapper.VimOrderKeyMapper">
    
    <resultMap type="VimOrderKey" id="VimOrderKeyResult">
        <result property="id"    column="id"    />
        <result property="uid"    column="uid"    />
        <result property="amount"    column="amount"    />
        <result property="balance"    column="balance"    />
        <result property="time"    column="time"    />
        <result property="info"    column="info"    />
    </resultMap>

    <!-- 用户钥匙消费扩展结果映射，包含用户信息 -->
    <resultMap type="com.ruoyi.project.VimOrderKeySys.domain.vo.VimOrderKeyVO" id="VimOrderKeyVOResult" extends="VimOrderKeyResult">
        <result property="userNickname"    column="user_nickname"    />
        <result property="userPhoneRaw"    column="user_phone"    />
    </resultMap>

    <sql id="selectVimOrderKeyVo">
        select id, uid, amount, balance, time, info from vim_order_key
    </sql>

    <!-- 包含用户信息的用户钥匙消费查询SQL -->
    <sql id="selectVimOrderKeyWithUserVo">
        SELECT
            vok.id,
            vok.uid,
            vok.amount,
            vok.balance,
            vok.time,
            vok.info,
            vu.nickname AS user_nickname,
            vu.phone AS user_phone
        FROM
            vim_order_key vok
                LEFT JOIN vim_user vu ON vok.uid = vu.id
    </sql>

    <select id="selectVimOrderKeyList" parameterType="VimOrderKey" resultMap="VimOrderKeyResult">
        <include refid="selectVimOrderKeyVo"/>
        <where>  
            <if test="uid != null "> and uid = #{uid}</if>
            <if test="amount != null "> and amount = #{amount}</if>
            <if test="balance != null "> and balance = #{balance}</if>
            <if test="time != null "> and time = #{time}</if>
            <if test="info != null  and info != ''"> and info like concat('%', #{info}, '%')</if>
        </where>
        order by time desc
    </select>

    <!-- 查询用户钥匙消费列表（包含用户信息） -->
    <select id="selectVimOrderKeyWithUserList" parameterType="VimOrderKey" resultMap="VimOrderKeyVOResult">
        <include refid="selectVimOrderKeyWithUserVo"/>
        <where>
            <if test="uid != null "> and vok.uid = #{uid}</if>
            <if test="amount != null "> and vok.amount = #{amount}</if>
            <if test="balance != null "> and vok.balance = #{balance}</if>
            <if test="time != null "> and vok.time = #{time}</if>
            <if test="info != null  and info != ''"> and vok.info like concat('%', #{info}, '%')</if>
        </where>
        order by vok.time desc
    </select>
    
    <select id="selectVimOrderKeyById" parameterType="String" resultMap="VimOrderKeyResult">
        <include refid="selectVimOrderKeyVo"/>
        where id = #{id}
    </select>

    <insert id="insertVimOrderKey" parameterType="VimOrderKey">
        insert into vim_order_key
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="uid != null">uid,</if>
            <if test="amount != null">amount,</if>
            <if test="balance != null">balance,</if>
            <if test="time != null">time,</if>
            <if test="info != null">info,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="uid != null">#{uid},</if>
            <if test="amount != null">#{amount},</if>
            <if test="balance != null">#{balance},</if>
            <if test="time != null">#{time},</if>
            <if test="info != null">#{info},</if>
         </trim>
    </insert>

    <update id="updateVimOrderKey" parameterType="VimOrderKey">
        update vim_order_key
        <trim prefix="SET" suffixOverrides=",">
            <if test="uid != null">uid = #{uid},</if>
            <if test="amount != null">amount = #{amount},</if>
            <if test="balance != null">balance = #{balance},</if>
            <if test="time != null">time = #{time},</if>
            <if test="info != null">info = #{info},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteVimOrderKeyById" parameterType="String">
        delete from vim_order_key where id = #{id}
    </delete>

    <delete id="deleteVimOrderKeyByIds" parameterType="String">
        delete from vim_order_key where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
