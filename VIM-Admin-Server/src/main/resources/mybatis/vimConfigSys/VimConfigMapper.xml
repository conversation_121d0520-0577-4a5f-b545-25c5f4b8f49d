<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.vimConfigSys.mapper.VimConfigMapper">
    
    <resultMap type="VimConfig" id="VimConfigResult">
        <result property="id"    column="id"    />
        <result property="system"    column="system"    />
        <result property="state"    column="state"    />
    </resultMap>

    <sql id="selectVimConfigVo">
        select id, `system`, state from vim_config
    </sql>

    <select id="selectVimConfigList" parameterType="VimConfig" resultMap="VimConfigResult">
        <include refid="selectVimConfigVo"/>
        <where>
            <if test="system != null and system != ''"> and `system` like concat('%', #{system}, '%')</if>
            <if test="state != null"> and state = #{state}</if>
        </where>
        order by id asc
    </select>
    
    <select id="selectVimConfigById" parameterType="Long" resultMap="VimConfigResult">
        <include refid="selectVimConfigVo"/>
        where id = #{id}
    </select>

    <update id="updateVimConfig" parameterType="VimConfig">
        update vim_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="system != null and system != ''">`system` = #{system},</if>
            <if test="state != null">state = #{state},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateVimConfigStatus">
        update vim_config set state = #{state} where id = #{id}
    </update>

</mapper>
