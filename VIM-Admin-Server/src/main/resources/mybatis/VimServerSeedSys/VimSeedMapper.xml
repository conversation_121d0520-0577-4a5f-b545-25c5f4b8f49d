<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.VimServerSeedSys.mapper.VimSeedMapper">
    
    <resultMap type="VimSeed" id="VimSeedResult">
        <result property="id"    column="id"    />
        <result property="seed"    column="seed"    />
        <result property="salt"    column="salt"    />
        <result property="createTime"    column="create_time"    />
        <result property="failTime"    column="fail_time"    />
    </resultMap>

    <sql id="selectVimSeedVo">
        select id, seed, salt, create_time, fail_time from vim_seed order by fail_time
    </sql>

    <select id="selectVimSeedList" parameterType="VimSeed" resultMap="VimSeedResult">
        <include refid="selectVimSeedVo"/>
        <where>
            <if test="seed != null  and seed != ''"> and seed = #{seed}</if>
            <if test="salt != null  and salt != ''"> and salt = #{salt}</if>
            <if test="failTime != null "> and fail_time = #{failTime}</if>
        </where>
    </select>
    
    <select id="selectVimSeedById" parameterType="Long" resultMap="VimSeedResult">
        <include refid="selectVimSeedVo"/>
        where id = #{id}
    </select>

    <insert id="insertVimSeed" parameterType="VimSeed" useGeneratedKeys="true" keyProperty="id">
        insert into vim_seed
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="seed != null">seed,</if>
            <if test="salt != null">salt,</if>
            <if test="createTime != null">create_time,</if>
            <if test="failTime != null">fail_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="seed != null">#{seed},</if>
            <if test="salt != null">#{salt},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="failTime != null">#{failTime},</if>
         </trim>
    </insert>

    <update id="updateVimSeed" parameterType="VimSeed">
        update vim_seed
        <trim prefix="SET" suffixOverrides=",">
            <if test="seed != null">seed = #{seed},</if>
            <if test="salt != null">salt = #{salt},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="failTime != null">fail_time = #{failTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteVimSeedById" parameterType="Long">
        delete from vim_seed where id = #{id}
    </delete>

    <delete id="deleteVimSeedByIds" parameterType="String">
        delete from vim_seed where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="findLatestSeed" parameterType="VimSeed" resultMap="VimSeedResult">
        SELECT * FROM vim_seed ORDER BY create_time DESC LIMIT 1
    </select>

    <!-- 根据盐值查询种子 -->
    <select id="findBySalt" parameterType="String" resultMap="VimSeedResult">
        <include refid="selectVimSeedVo"/>
        where salt = #{salt} and fail_time is null
        order by create_time desc
        limit 1
    </select>

    <!-- 根据种子值和盐值查询种子记录（用于验证） -->
    <select id="findBySeedAndSalt" resultMap="VimSeedResult">
        <include refid="selectVimSeedVo"/>
        where seed = #{seed} and salt = #{salt}
        order by create_time desc
        limit 1
    </select>
</mapper>