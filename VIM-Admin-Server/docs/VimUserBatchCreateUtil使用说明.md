# VIM用户批量创建工具使用说明

## 概述

`VimUserBatchCreateUtil` 是一个专门用于批量创建VIM系统测试用户的工具类。该工具确保测试用户与正式用户数据不冲突，并提供了丰富的功能特性。

## 功能特点

### 1. 用户ID分离设计
- **测试用户ID范围**: 1-199
- **正式用户ID范围**: 200+
- 避免测试数据与正式数据冲突

### 2. 二次元风格昵称
- 使用预定义的二次元风格前缀和后缀
- 自动添加随机数字确保唯一性
- 示例：`星空酱1234`、`月影君5678`

### 3. 随机头像分配
- 从8个预定义头像URL中随机选择
- 头像地址：`http://www.voltskins.top/image/userimages/01.png` ~ `08.png`

### 4. 唯一性保证
- 自动检查phone和nickname的唯一性约束
- 智能分配连续可用的用户ID
- 避免与现有用户数据冲突

### 5. 事务安全
- 使用`@Transactional`注解确保数据一致性
- 批量操作失败时自动回滚

### 6. 详细日志
- 提供创建进度和结果的详细日志
- 记录成功和失败的用户信息

## 使用方法

### 1. 通过Controller接口使用

```http
POST /tool/batchCreate/users/{count}
```

**参数说明:**
- `count`: 创建用户数量（1-199）

**示例:**
```bash
curl -X POST "http://localhost:8080/tool/batchCreate/users/30" \
  -H "Authorization: Bearer your-token"
```

### 2. 直接调用工具类

```java
@Autowired
private VimUserBatchCreateUtil batchCreateUtil;

public void createTestUsers() {
    try {
        // 创建30个测试用户
        VimUserBatchCreateUtil.BatchCreateResult result = 
            batchCreateUtil.batchCreateUsers(30);
        
        System.out.println("创建结果: " + result.toString());
        System.out.println("成功用户: " + result.getSuccessUsers());
        System.out.println("失败用户: " + result.getFailedUsers());
        
    } catch (Exception e) {
        System.err.println("创建失败: " + e.getMessage());
    }
}
```

## 生成的用户数据结构

### 基础信息
- **ID**: 1-199范围内的连续可用ID
- **手机号**: `1{用户ID补齐10位}` 格式
- **用户名**: `user{用户ID}` 格式
- **密码**: 默认`123456`（经过HashUtil加密）
- **昵称**: 二次元风格随机生成

### 默认值设置
- **货币(coin)**: 0.00
- **钥匙(key)**: 0.00
- **身份(identity)**: 1（普通用户）
- **状态(state)**: 1（正常）
- **实名认证(isauth)**: 0（未实名）
- **经验(exp)**: 0.00
- **等级(level)**: 0

### 其他字段
- **头像**: 随机选择的8个头像之一
- **邀请码**: `VIM{用户ID补齐6位}` 格式
- **创建时间**: 当前时间戳（秒级）
- **种子**: 自动生成的用户种子

## 限制和约束

### 1. 数量限制
- 单次最多创建199个用户
- 总测试用户数不能超过199个

### 2. ID范围限制
- 测试用户ID严格限制在1-199范围内
- 超出范围会抛出异常

### 3. 唯一性约束
- 手机号必须唯一
- 昵称必须唯一
- 如果无法找到足够的连续可用ID，会抛出异常

## 错误处理

### 常见错误及解决方案

1. **"创建数量必须在1-199之间"**
   - 检查传入的count参数是否在有效范围内

2. **"无法找到连续可用的ID"**
   - 测试用户ID范围已满，需要清理现有测试用户
   - 或者减少创建数量

3. **"手机号/昵称重复"**
   - 数据库中已存在相同的手机号或昵称
   - 工具会自动跳过并记录失败信息

## 数据清理

### 手动清理测试用户
```sql
-- 清理测试用户数据（谨慎使用）
DELETE FROM vim_user WHERE id BETWEEN 1 AND 199;

-- 重置自增ID（可选）
ALTER TABLE vim_user AUTO_INCREMENT = 200;
```

### 查询测试用户使用情况
```sql
-- 查看已使用的测试用户ID
SELECT id, nickname, phone, create_time 
FROM vim_user 
WHERE id BETWEEN 1 AND 199 
ORDER BY id;

-- 统计测试用户数量
SELECT COUNT(*) as test_user_count 
FROM vim_user 
WHERE id BETWEEN 1 AND 199;
```

## 注意事项

1. **生产环境使用**: 该工具主要用于开发和测试环境，生产环境使用需谨慎
2. **权限控制**: 建议配置适当的权限控制，避免误操作
3. **数据备份**: 使用前建议备份数据库
4. **性能考虑**: 大批量创建时可能影响数据库性能
5. **日志监控**: 注意监控创建过程的日志输出

## 扩展功能

如需扩展功能，可以修改以下部分：

1. **昵称生成规则**: 修改`ANIME_PREFIXES`和`ANIME_SUFFIXES`数组
2. **头像URL**: 修改`AVATAR_URLS`数组
3. **默认值设置**: 修改`createSingleUser`方法中的默认值
4. **ID范围**: 修改相关常量和验证逻辑

## 技术实现

- **Spring Boot**: 基于Spring Boot框架
- **MyBatis**: 使用MyBatis进行数据库操作
- **事务管理**: Spring事务管理
- **日志框架**: SLF4J + Logback
- **API文档**: Swagger注解支持
