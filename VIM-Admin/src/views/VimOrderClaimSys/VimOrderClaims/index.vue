<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" class="search-form">
      <el-row :gutter="15">
        <!-- 按照发货时间倒叙或者默认排序 -->
        <el-col :xs="24" :sm="12" :md="6" :lg="6">
          <el-form-item label="排序" prop="orderBy" class="search-item">
            <el-select
                v-model="queryParams.orderBy"
                placeholder="请选择排序方式"
                clearable
                size="default"
                style="width: 100%"
                @change="handleSortChange">
              <el-option label="默认排序" value="default" />
              <el-option label="发货时间倒序" value="claimTimeDesc" />
              <el-option label="用户等级升序" value="userLevelAsc" />
              <el-option label="用户等级降序" value="userLevelDesc" />
            </el-select>
          </el-form-item>
        </el-col>
        <!-- 发货时间范围选择器 -->
        <el-col :xs="24" :sm="12" :md="12" :lg="8">
          <el-form-item label="发货时间" prop="claimTimeRange" class="search-item">
            <el-date-picker
                v-model="dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="YYYY-MM-DD"
                style="width: 100%"
                @change="handleDateRangeChange"
            />
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8" :lg="6">
          <el-form-item label="提货订单ID" prop="id" class="search-item">
            <el-input
                v-model="queryParams.id"
                placeholder="请输入提货订单ID"
                clearable
                @keyup.enter="handleQuery"
            />
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8" :lg="6">
          <el-form-item label="物品订单ID" prop="oid" class="search-item">
            <el-input
                v-model="queryParams.oid"
                placeholder="请输入物品订单ID"
                clearable
                @keyup.enter="handleQuery"
            />
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8" :lg="6">
          <el-form-item label="用户ID" prop="uid" class="search-item">
            <el-input
                v-model="queryParams.uid"
                placeholder="请输入用户ID"
                clearable
                @keyup.enter="handleQuery"
            />
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8" :lg="6">
          <el-form-item label="状态" prop="state" class="search-item">
            <el-select
                v-model="queryParams.state"
                placeholder="请选择状态"
                clearable
                style="width: 100%"
            >
              <el-option label="发货中" :value="1" />
              <el-option label="已发货" :value="2" />
              <el-option label="发货失败" :value="3" />
              <el-option label="已锁价未发货" :value="4" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8" :lg="6">
          <el-form-item label="用户手机号" prop="userPhone" class="search-item">
            <el-input
                v-model="queryParams.userPhone"
                placeholder="请输入用户手机号"
                clearable
                @keyup.enter="handleQuery"
            />
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8" :lg="6">
          <el-form-item class="search-actions" style="margin-left: 18px;margin-top: 18px">
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <el-table v-loading="loading" :data="VimOrderClaimsList" @selection-change="handleSelectionChange" :row-class-name="getRowClassName">
      <el-table-column label="提货订单id" align="center" prop="id" />
      <el-table-column label="物品订单id" align="center" prop="oid" />
      <el-table-column label="用户id" align="center" prop="uid" width="70"/>
      <el-table-column label="商品id" align="center" prop="itemid" width="70"/>
      <el-table-column label="商品名" align="center" prop="itemname" />
      <el-table-column label="商品价格" align="center" prop="itemPrice" />
      <el-table-column label="商品英文名" align="center" prop="hashname" />
      <el-table-column label="用户昵称" align="center" prop="userDisplayName" width="70"/>
      <el-table-column label="用户手机号" align="center" prop="userPhoneRaw" width="150">
        <template #default="scope">
          <div style="display: flex; align-items: center; justify-content: center;">
            <span>{{ scope.row.userPhoneRaw }}</span>
            <!-- <el-button
              v-hasPermi="['VimUserSys:VimUsers:query']"
              v-if="scope.row.userPhoneRaw"
              type="primary"
              link
              icon="CopyDocument"
              @click.stop="copyUserPhone(scope.row.userPhoneRaw)"
              title="复制完整手机号"
            ></el-button> -->
          </div>
        </template>
      </el-table-column>
      <el-table-column label="用户身份" align="center" prop="userIdentity" width="100">
        <template #default="scope">
          <el-tag
            :type="getIdentityTagType(scope.row.userIdentity)"
            size="small">
            {{ getIdentityDesc(scope.row.userIdentity) }}
          </el-tag>
        </template>
      </el-table-column>
      <!-- 🆕 新增用户等级列 -->
      <el-table-column
        label="用户等级"
        align="center"
        prop="userLevel"
        width="100"
        sortable="custom"
        @sort-change="handleUserLevelSort">
        <template #default="scope">
          <el-tag v-if="scope.row.userLevel !== null && scope.row.userLevel !== undefined"
                  type="primary"
                  size="small">
            {{ scope.row.userLevel }}级
          </el-tag>
          <span v-else class="no-data">-</span>
        </template>
      </el-table-column>
      <!-- 🆕 新增用户经验列 -->
      <el-table-column label="用户经验" align="center" prop="userExp" width="100">
        <template #default="scope">
          <el-tag v-if="scope.row.userExp !== null && scope.row.userExp !== undefined"
                  type="success"
                  size="small">
            {{ scope.row.userExp }} EXP
          </el-tag>
          <span v-else class="no-data">-</span>
        </template>
      </el-table-column>
      <el-table-column label="提交时间" align="center" prop="creatTime" width="100">
        <template #default="scope">
          {{ formatTimestamp(scope.row.creatTime) }}
        </template>
      </el-table-column>
      <el-table-column label="发货时间" align="center" prop="claimTime" width="100">
        <template #default="scope">
          {{ formatTimestamp(scope.row.claimTime) }}
        </template>
      </el-table-column>

      <el-table-column label="状态" align="center" prop="state" width="90">
        <template #default="scope">
          <el-tag
            :type="getStatusTagType(scope.row.state)"
            size="small">
            {{ getStatusText(scope.row.state) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="160">
        <template #default="scope">
          <div style="display: flex; flex-direction: column; gap: 4px;">
            <div>
              <el-button
                  v-if="scope.row.state == 1"
                  type="primary"
                  size="small"
                  @click="handleShip(scope.row)">
                发货
              </el-button>
              <el-button
                  v-if="scope.row.state == 1"
                  type="warning"
                  size="small"
                  @click="handleLockPrice(scope.row)"
                  style="margin-left: 8px;">
                锁价
              </el-button>
              <el-button
                  v-else-if="scope.row.state == 2"
                  type="info"
                  size="small"
                  @click="handleDetail(scope.row)">
                详情
              </el-button>
              <el-button
                  v-else-if="scope.row.state == 3"
                  type="warning"
                  size="small"
                  @click="handleDetail(scope.row)">
                <el-icon><WarningFilled /></el-icon>
                查看失败原因
              </el-button>
              <el-button
                  v-else-if="scope.row.state == 4"
                  type="info"
                  size="small"
                  @click="handleDetail(scope.row)">
                详情
              </el-button>
            </div>
            <div v-if="scope.row.state == 2">
              <el-button
                  type="success"
                  size="small"
                  @click="handleEditCost(scope.row)"
                  v-hasPermi="['VimOrderClaimSys:VimOrderClaims:edit']">
                修改发货价格
              </el-button>
            </div>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <!-- 发货弹窗 -->
    <el-dialog title="发货信息" v-model="shipDialogVisible" width="600px">
      <el-form :model="shipForm" label-width="120px">
        <el-form-item label="Steam ID:">
          <el-input v-model="shipForm.steamid" readonly />
        </el-form-item>
        <el-form-item label="Steam 交易链接:">
          <el-input v-model="shipForm.steamlink" type="textarea" readonly />
        </el-form-item>
        <el-form-item label="实际发货金额:" prop="cost">
          <el-input-number
              v-model="shipForm.cost"
              :min="0"
              :precision="2"
              :step="0.01"
              placeholder="请输入实际发货金额"
              style="width: 100%" />
        </el-form-item>
        <el-form-item label="失败原因:" prop="failReason" v-if="showFailReason">
          <el-input
              v-model="shipForm.failReason"
              type="textarea"
              placeholder="请输入发货失败原因"
              :rows="3" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="shipDialogVisible = false">取消</el-button>
          <el-button
              type="danger"
              @click="showFailReason = true"
              v-if="!showFailReason">
            发货失败
          </el-button>
          <el-button
              type="danger"
              @click="handleFailShip"
              :disabled="!shipForm.failReason"
              v-else>
            确认失败
          </el-button>
          <el-button type="primary" @click="confirmShip">确认发货</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 详情弹窗 -->
    <el-dialog
      :title="detailForm.orderState === 3 ? '发货失败详情' : '订单详情'"
      v-model="detailDialogVisible"
      width="600px"
    >
      <!-- 失败状态显示失败原因 -->
      <div v-if="detailForm.orderState === 3" class="fail-reason-container">
        <el-alert
          title="发货失败"
          type="error"
          :closable="false"
          show-icon
          class="mb-4"
        >
          <template #default>
            <div class="fail-info">
              <p><strong>订单ID：</strong>{{ detailForm.orderId }}</p>
              <p><strong>商品名称：</strong>{{ detailForm.itemname }}</p>
            </div>
          </template>
        </el-alert>

        <el-card class="fail-reason-card" shadow="never">
          <template #header>
            <div class="card-header">
              <el-icon class="warning-icon"><WarningFilled /></el-icon>
              <span>失败原因详情</span>
            </div>
          </template>
          <div class="fail-reason-content">
            <el-input
              v-model="detailForm.failReason"
              type="textarea"
              :rows="4"
              readonly
              placeholder="暂无失败原因信息"
              class="fail-reason-textarea"
            />
          </div>
        </el-card>
      </div>

      <!-- 正常状态显示详细信息 -->
      <el-form v-else :model="detailForm" label-width="120px">
        <el-form-item label="订单ID:">
          <el-input v-model="detailForm.orderId" readonly />
        </el-form-item>
        <el-form-item label="商品名称:">
          <el-input v-model="detailForm.itemname" readonly />
        </el-form-item>
        <el-form-item label="Steam ID:">
          <el-input v-model="detailForm.steamid" readonly />
        </el-form-item>
        <el-form-item label="Steam 交易链接:">
          <el-input v-model="detailForm.steamlink" type="textarea" readonly />
        </el-form-item>
        <el-form-item label="实际发货价格:">
          <el-input v-model="detailForm.actualCost" readonly>
            <template #prepend>¥</template>
          </el-input>
        </el-form-item>
        <el-form-item v-if="detailForm.orderInfo" label="订单备注:">
          <el-input v-model="detailForm.orderInfo" type="textarea" readonly />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 修改发货价格弹窗 -->
    <el-dialog title="修改实际发货价格" v-model="editCostDialogVisible" width="500px">
      <el-form :model="editCostForm" :rules="editCostRules" ref="editCostFormRef" label-width="120px">
        <el-form-item label="订单ID:">
          <el-input v-model="editCostForm.orderId" readonly />
        </el-form-item>
        <el-form-item label="商品名称:">
          <el-input v-model="editCostForm.itemname" readonly />
        </el-form-item>
        <el-form-item label="当前发货价格:">
          <el-input v-model="editCostForm.currentCost" readonly />
        </el-form-item>
        <el-form-item label="新发货价格:" prop="newCost">
          <el-input-number
              v-model="editCostForm.newCost"
              :min="0"
              :precision="2"
              :step="0.01"
              placeholder="请输入新的实际发货价格"
              style="width: 100%" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="editCostDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmEditCost">确认修改</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 锁价确认弹窗 -->
    <el-dialog title="锁价确认" v-model="lockPriceDialogVisible" width="500px">
      <el-form :model="lockPriceForm" label-width="120px">
        <el-form-item label="订单ID:">
          <el-input v-model="lockPriceForm.orderId" readonly />
        </el-form-item>
        <el-form-item label="商品名称:">
          <el-input v-model="lockPriceForm.itemname" readonly />
        </el-form-item>
        <el-form-item label="Steam 交易链接:">
          <el-input v-model="lockPriceForm.steamlink" type="textarea" readonly />
        </el-form-item>
        <el-form-item label="锁定价格">
          <el-input-number v-model="lockPriceForm.cost" />
        </el-form-item>
        <el-alert
          title="确认锁价操作"
          description="锁价后订单状态将变更为'已锁价未发货'，请确认是否继续？"
          type="warning"
          show-icon
          :closable="false"
          style="margin: 20px 0;" />
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="lockPriceDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmLockPrice">确认锁价</el-button>
        </div>
      </template>
    </el-dialog>

    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />


  </div>
</template>

<script setup name="VimOrderClaims">
import { ref, reactive, toRefs, getCurrentInstance, onMounted } from 'vue';
import { listVimOrderClaims, updateVimOrderClaims } from "@/api/VimOrderClaimSys/VimOrderClaims";
import { getUserInfo } from "@/api/VimUserSys/VimUsers";
import { WarningFilled, InfoFilled } from '@element-plus/icons-vue';

const { proxy } = getCurrentInstance();

// 响应式数据定义
const VimOrderClaimsList = ref([]);
const loading = ref(true);
const showSearch = ref(true);
const total = ref(0);
const shipDialogVisible = ref(false);
const detailDialogVisible = ref(false);
const editCostDialogVisible = ref(false);
const lockPriceDialogVisible = ref(false);
const showFailReason = ref(false);
const dateRange = ref([]); // 发货时间日期范围
// 用户身份描述映射
const identityMap = {
  1: '普通用户',
  2: '线上主播',
  3: '线下主播'
};

// 获取用户身份描述
const getIdentityDesc = (identity) => {
  return identityMap[identity] || '未知身份';
};

// 获取用户身份标签类型
const getIdentityTagType = (identity) => {
  switch(identity) {
    case 1: return 'info';     // 普通用户 - 灰色
    case 2: return 'success';  // 线上主播 - 绿色
    case 3: return 'warning';  // 线下主播 - 橙色
    default: return 'info';    // 默认 - 灰色
  }
};
// 表单数据
const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    id: null,
    oid: null,
    uid: null,
    state: null,
    userPhone: null,
    orderBy: 'default',
    beginClaimTime: null, // 发货时间范围开始
    endClaimTime: null    // 发货时间范围结束
  },
  rules: {},
  shipForm: {
    uid: null,
    steamid: null,
    steamlink: null,
    orderId: null,
    failReason: null,
    cost: null,
  },
  detailForm: {
    orderId: null,
    itemname: null,
    steamid: null,
    steamlink: null,
    actualCost: null,
    orderState: null,
    failReason: null,
    orderInfo: null
  },
  editCostForm: {
    orderId: null,
    itemname: null,
    currentCost: null,
    newCost: null
  },
  lockPriceForm: {
    orderId: null,
    itemname: null,
    steamlink: null,
    cost: 0,
    uid: null,
  },
  editCostRules: {
    newCost: [
      { required: true, message: "新发货价格不能为空", trigger: "blur" },
      { type: 'number', min: 0, message: "发货价格必须大于等于0", trigger: "blur" }
    ]
  }
});

const { queryParams, form, rules, shipForm, detailForm, editCostForm, lockPriceForm, editCostRules } = toRefs(data);




const statusMap = {
  1: '发货中',
  2: '已发货',
  3: '发货失败',
  4: '已锁价未发货'
};

// 注释：初始化数据加载已移至onMounted中，避免重复请求

/** 查询订单发货列表 */
function getList() {
  loading.value = true;
  listVimOrderClaims(queryParams.value)
      .then(response => {
        // 确保state字段为数字类型，解决按钮显示问题
        const rows = (response.rows || []).map(item => ({
          ...item,
          state: Number(item.state) // 强制转换为数字类型
        }));
        VimOrderClaimsList.value = rows;
        total.value = response.total || 0;
      })
      .catch(error => {
        console.error("获取订单列表失败:", error);
        proxy.$modal.msgError("获取订单列表失败");
      })
      .finally(() => {
        loading.value = false;
      });
}

/** 获取表格行的类名，用于标记特定Steam链接的行 */
const getRowClassName = ({ row }) => {
  if (row.steamlink === 'https://steamcommunity.com/tradeoffer/new/?partner=1513331812&token=1QziIKpO') {
    return 'highlighted-row';
  }
  return '';
};

/** 格式化时间戳 */
const formatTimestamp = (timestamp) => {
  if (!timestamp) return '-';
  const ts = timestamp.toString().length === 10 ? timestamp * 1000 : timestamp;
  const date = new Date(ts);
  const padZero = num => num.toString().padStart(2, '0');

  return `${date.getFullYear()}-${padZero(date.getMonth() + 1)}-${padZero(date.getDate())} ${padZero(date.getHours())}:${padZero(date.getMinutes())}:${padZero(date.getSeconds())}`;
};

/** 获取状态文本 */
const getStatusText = (state) => {
  return statusMap[state] || state;
};

/** 获取状态标签类型 */
const getStatusTagType = (state) => {
  switch(state) {
    case 1: return 'warning';  // 发货中 - 橙色
      case 2: return 'success';  // 已发货 - 绿色
      case 3: return 'danger';   // 发货失败 - 红色
      case 4: return 'info';     // 已锁价未发货 - 蓝色
    default: return 'info';    // 默认 - 灰色
  }
};

/** 处理发货失败 */
const handleFailShip = async () => {
  try {
    loading.value = true;
    const res = await updateVimOrderClaims({
      id: shipForm.value.orderId,
      state: 3, // 状态设为3表示发货失败
      info: shipForm.value.failReason,
      oid: shipForm.value.oid,
      // claimTime: Math.floor(Date.now() / 1000)
    });
    if (res.code === 200) {
      proxy.$modal.msgSuccess("已标记为发货失败");
      shipDialogVisible.value = false;
      showFailReason.value = false;
      getList(); // 刷新列表
    } else {
      proxy.$modal.msgError(res.msg || "操作失败");
    }
  } catch (error) {
    console.error("操作失败:", error);
    proxy.$modal.msgError("操作失败");
  } finally {
    loading.value = false;
  }
};

/** 发货操作 */
const handleShip = async (row) => {
  try {
    loading.value = true;
    showFailReason.value = false; // 重置显示状态

    // 检查订单状态，防止重复发货
    if (row.state == 2) {
      proxy.$modal.msgWarning("该订单已发货，无法重复发货");
      return;
    }

    if (row.state == 3) {
      proxy.$modal.msgWarning("该订单发货失败，请先处理失败状态");
      return;
    }

    if (row.state != 1) {
      proxy.$modal.msgWarning("订单状态异常，无法进行发货操作");
      return;
    }

    const res = await getUserInfo(row.uid);
    if (res.code === 200) {
      shipForm.value = {
        uid: row.uid,
        steamid: res.data.steamId,
        steamlink: res.data.steamLink,
        orderId: row.id,
        failReason: '', // 清空失败原因
        oid: row.oid,
        cost: null // 清空实际发货金额
      };
      shipDialogVisible.value = true;
    } else {
      proxy.$modal.msgError(res.msg || "获取用户信息失败");
    }
  } catch (error) {
    console.error("获取用户信息失败:", error);
    proxy.$modal.msgError("获取用户信息失败");
  } finally {
    loading.value = false;
  }
};

/** 确认发货 */
const confirmShip = async () => {
  try {
    loading.value = true;
    const res = await updateVimOrderClaims({
      id: shipForm.value.orderId,  // 订单ID
      steamid: shipForm.value.steamid,  // SteamID
      steamlink: shipForm.value.steamlink,  // Steam交易链接
      state: 2,
      oid:shipForm.value.oid,
      cost: shipForm.value.cost,  // 实际发货金额
      claimTime: Math.floor(Date.now() / 1000)  // 当前时间戳
    });
    if (res.code === 200) {
      proxy.$modal.msgSuccess("发货成功");
      shipDialogVisible.value = false;
      getList();  // 刷新列表
    } else {
      proxy.$modal.msgError(res.msg || "发货失败");
    }
  } catch (error) {
    console.error("发货失败:", error);
    proxy.$modal.msgError("发货失败");
  } finally {
    loading.value = false;
  }
};

/** 查看详情 */
const handleDetail = async (row) => {
  try {
    loading.value = true;
    
    // 获取用户信息以确保Steam信息是最新的
    const res = await getUserInfo(row.uid);
    if (res.code === 200) {
      detailForm.value.orderId = row.id;
      detailForm.value.itemname = row.itemname;
      detailForm.value.steamid = res.data.steamId || '未设置';
      detailForm.value.steamlink = res.data.steamLink || '未设置';
      detailForm.value.actualCost = row.cost ? Number(row.cost).toFixed(2) : '未设置';
      detailForm.value.orderState = Number(row.state);
      detailForm.value.failReason = row.info || '暂无失败原因信息';
      detailForm.value.orderInfo = row.info;
      detailDialogVisible.value = true;
    } else {
      proxy.$modal.msgError(res.msg || "获取用户信息失败");
    }
  } catch (error) {
    console.error("获取用户信息失败:", error);
    proxy.$modal.msgError("获取用户信息失败");
  } finally {
    loading.value = false;
  }
};

/** 修改发货价格操作 */
const handleEditCost = (row) => {
  editCostForm.value = {
    orderId: row.id,
    itemname: row.itemname,
    currentCost: row.cost ? `¥${Number(row.cost).toFixed(2)}` : '未设置',
    newCost: row.cost ? Number(row.cost) : 0
  };
  editCostDialogVisible.value = true;
};

/** 确认修改发货价格 */
const confirmEditCost = async () => {
  try {
    // 表单验证
    await proxy.$refs.editCostFormRef.validate();

    loading.value = true;
    const res = await updateVimOrderClaims({
      id: editCostForm.value.orderId,
      cost: editCostForm.value.newCost
    });

    if (res.code === 200) {
      proxy.$modal.msgSuccess("发货价格修改成功");
      editCostDialogVisible.value = false;
      getList(); // 刷新列表
    } else {
      proxy.$modal.msgError(res.msg || "修改失败");
    }
  } catch (error) {
    if (error !== false) { // 排除表单验证失败的情况
      console.error("修改发货价格失败:", error);
      proxy.$modal.msgError("修改失败");
    }
  } finally {
    loading.value = false;
  }
};

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = []; // 重置日期范围
  proxy.resetForm("queryRef");
  queryParams.value.orderBy = 'default'; // 重置排序为默认值
  queryParams.value.beginClaimTime = null; // 重置开始时间
  queryParams.value.endClaimTime = null; // 重置结束时间
  handleQuery();
}

/** 排序变化处理 */
function handleSortChange() {
  // 保存排序状态到localStorage
  localStorage.setItem('vimOrderClaimSortOrder', queryParams.value.orderBy);
  queryParams.value.pageNum = 1; // 重置到第一页
  getList();
}

/** 用户等级列排序处理 */
function handleUserLevelSort({ column, prop, order }) {
  if (order === 'ascending') {
    queryParams.value.orderBy = 'userLevelAsc';
  } else if (order === 'descending') {
    queryParams.value.orderBy = 'userLevelDesc';
  } else {
    queryParams.value.orderBy = 'default';
  }
  // 保存排序状态到localStorage
  localStorage.setItem('vimOrderClaimSortOrder', queryParams.value.orderBy);
  queryParams.value.pageNum = 1; // 重置到第一页
  getList();
}

/** 处理日期范围变化 */
function handleDateRangeChange(val) {
  if (val) {
    // 将日期字符串转换为时间戳（秒）
    const startDate = new Date(val[0]);
    const endDate = new Date(val[1]);
    startDate.setHours(0, 0, 0, 0);
    endDate.setHours(23, 59, 59, 999);
    
    queryParams.value.beginClaimTime = Math.floor(startDate.getTime() / 1000);
    queryParams.value.endClaimTime = Math.floor(endDate.getTime() / 1000);
  } else {
    queryParams.value.beginClaimTime = null;
    queryParams.value.endClaimTime = null;
  }
}

/** 复制用户完整手机号 */
function copyUserPhone(phone) {
  if (!phone) {
    proxy.$modal.msgError("手机号为空，无法复制");
    return;
  }
  
  // 使用Clipboard API复制文本
  navigator.clipboard.writeText(phone)
    .then(() => {
      proxy.$modal.msgSuccess("手机号已复制到剪贴板");
    })
    .catch(err => {
      console.error("复制失败:", err);
      proxy.$modal.msgError("复制失败，请手动复制");
    });
}

/** 锁价操作 */
const handleLockPrice = async (row) => {
  try {
    loading.value = true;

    // 检查订单状态
    if (row.state !== 1) {
      proxy.$modal.msgWarning("只有发货中的订单才能进行锁价操作");
      return;
    }

    const res = await getUserInfo(row.uid);
    if (res.code === 200) {
      lockPriceForm.value = {
        orderId: row.id,
        itemname: row.itemname,
        steamlink: res.data.steamLink,
        cost: res.data.cost,
        uid: res.data.uid
      };
      lockPriceDialogVisible.value = true;
    } else {
      proxy.$modal.msgError(res.msg || "获取用户信息失败");
    }
  } catch (error) {
    console.error("获取用户信息失败:", error);
    proxy.$modal.msgError("获取用户信息失败");
  } finally {
    loading.value = false;
  }
};

/** 确认锁价 */
const confirmLockPrice = async () => {
  try {
    loading.value = true;
    const res = await updateVimOrderClaims({
      id: lockPriceForm.value.orderId,
      steamlink: lockPriceForm.value.steamlink,
      uid: lockPriceForm.value.uid,
      cost: lockPriceForm.value.cost,
      state: 4  // 更新状态为已锁价未发货
    });
    if (res.code === 200) {
      proxy.$modal.msgSuccess("锁价成功");
      lockPriceDialogVisible.value = false;
      getList();  // 刷新列表
    } else {
      proxy.$modal.msgError(res.msg || "锁价失败");
    }
  } catch (error) {
    console.error("锁价失败:", error);
    proxy.$modal.msgError("锁价失败");
  } finally {
    loading.value = false;
  }
};

// 页面初始化
onMounted(() => {
  // 从localStorage恢复排序状态
  const savedSortOrder = localStorage.getItem('vimOrderClaimSortOrder');
  if (savedSortOrder && savedSortOrder !== 'null') {
    queryParams.value.orderBy = savedSortOrder;
  }

  // 初始化数据
  getList();
});
</script>

<style scoped>
/* 新增的搜索区域样式 */
.search-form {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
}
.search-item {
  margin-bottom: 15px;
  width: 100%;
}
.search-item :deep(.el-form-item__label) {
  width: 100px !important;
  text-align: right;
  padding-right: 10px;
}
.search-item :deep(.el-input) {
  width: 100%;
}
.search-item :deep(.el-select) {
  width: 100%;
}
.search-item :deep(.el-select .el-input__wrapper) {
  border-radius: 4px;
  transition: all 0.3s ease;
}
.search-item :deep(.el-select .el-input__wrapper:hover) {
  border-color: #409eff;
}
.search-actions {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 0;
  padding-top: 2px;
}
/* 响应式调整 */
@media (max-width: 768px) {
  .search-item :deep(.el-form-item__label) {
    width: 80px !important;
  }
}
.dialog-footer {
  display: flex;
  justify-content: space-between;
}
.dialog-footer .el-button {
  min-width: 100px;
}
.el-form-item__content {
  margin-left: 100px !important;
}
.el-textarea__inner {
  min-height: 80px;
}

/* 🆕 用户等级和经验相关样式 */
.no-data {
  color: #c0c4cc;
  font-size: 12px;
  font-style: italic;
}

/* 用户等级标签样式 */
.el-tag.el-tag--primary {
  background-color: #ecf5ff;
  border-color: #b3d8ff;
  color: #409eff;
}

/* 用户经验标签样式 */
.el-tag.el-tag--success {
  background-color: #f0f9ff;
  border-color: #95de64;
  color: #67c23a;
}

/* 高亮显示特定Steam链接的行 */
:deep(.highlighted-row) {
  background-color: #fff9f3 !important;
}

:deep(.highlighted-row:hover) {
  background-color: #ffebd3 !important;
}

/* 失败原因容器样式 */
.fail-reason-container {
  padding: 0;
}

.fail-reason-container .mb-4 {
  margin-bottom: 16px;
}

.fail-info p {
  margin: 4px 0;
  font-size: 14px;
  color: #606266;
}

.fail-info strong {
  color: #303133;
}

/* 失败原因卡片样式 */
.fail-reason-card {
  margin-bottom: 16px;
  border: 1px solid #f56c6c;
}

.fail-reason-card :deep(.el-card__header) {
  background-color: #fef0f0;
  border-bottom: 1px solid #f56c6c;
}

.card-header {
  display: flex;
  align-items: center;
  font-weight: 600;
  color: #f56c6c;
}

.warning-icon {
  margin-right: 8px;
  font-size: 16px;
}

.fail-reason-content {
  padding: 8px 0;
}

.fail-reason-textarea :deep(.el-textarea__inner) {
  background-color: #fef0f0;
  border-color: #f56c6c;
  color: #f56c6c;
  font-weight: 500;
  resize: none;
}

/* 建议卡片样式 */
.suggestion-card {
  border: 1px solid #e6a23c;
}

.suggestion-card :deep(.el-card__header) {
  background-color: #fdf6ec;
  border-bottom: 1px solid #e6a23c;
}

.suggestion-card .card-header {
  color: #e6a23c;
}

.info-icon {
  margin-right: 8px;
  font-size: 16px;
}

.suggestion-content {
  padding: 8px 0;
}

.suggestion-list {
  margin: 0;
  padding-left: 20px;
  color: #606266;
}

.suggestion-list li {
  margin-bottom: 8px;
  line-height: 1.5;
}

.suggestion-list li:last-child {
  margin-bottom: 0;
}
</style>